﻿@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Search.Ui.Models
@model PrintPreviewPageModel

@{
    Layout = null;
    bool FiltersVisible = false;
}

<!DOCTYPE html>
<html lang="en">

<head>
    <title>VigiLit Search Results Print Preview</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <style>
        .instructions {
            background: #F0F8FF;
            border: 1px solid #1E90FF;
            border-radius: 5px;
            padding: 0 15px;
            margin-top: 20px;
        }

        @@media print {
            .instructions {
                display: none;
            }
        }

        h1, h2, h3, p, .filters div {
            font-family: sans-serif;
            margin: 0;
            padding: 0;
        }

        h1 {
            font-size: 21px;
        }

        h2 {
            font-size: 18px;
            margin-top: 30px;
        }

        h3 {
            font-size: 17px;
        }

        p {
            font-size: 14px;
            margin: 14px 0;
        }

        .filters div {
            font-size: 14px;
        }

        .container {
            margin: 0 auto;
            width: 1050px;
        }

        .header {
            margin-top: 30px;
        }

        .filters {
        }

            .filters b {
                display: inline-block;
                width: 140px;
            }

            .filters > div {
                margin: 14px 0;
            }

                .filters > div > div:first-child {
                    display: inline-block;
                    width: 140px;
                }

                .filters > div > div:last-child {
                    display: inline-block;
                    width: 900px;
                }

                    .filters > div > div:last-child > div {
                        display: flex;
                        flex-flow: row wrap;
                    }

                        .filters > div > div:last-child > div > div {
                            width: 25%;
                        }

        .results {
        }

        .classifications {
            margin-top: 30px;
        }

            .classifications > div {
                border-top: 2px solid #DDD;
                margin-bottom: 15px;
            }

                .classifications > div > div:first-child {
                    display: flex;
                    flex-direction: row;
                }

                    .classifications > div > div:first-child > div:first-child {
                        width: 76%;
                        padding: 18px 18px 5px 0;
                    }

                    .classifications > div > div:first-child > div:last-child {
                        width: 24%;
                        padding: 18px 0 5px 18px;
                        border-left: 1px solid #EEE;
                    }

                .classifications > div > div:last-child {
                    border-top: 1px solid #EEE;
                    border-bottom: 2px solid #DDD;
                }

                    .classifications > div > div:last-child span {
                        display: inline-block;
                        margin-right: 30px;
                    }

        .safetySpecificItems {
            display: flex;
            flex-direction: column;
            font-family: sans-serif;
            font-size: 14px;
        }

        .safetySpecificParagraph {
            margin-bottom: 0px;
        }

    </style>
</head>

<body>

<div class="container">

    <div class="instructions">
        <p>Press Ctrl+P or <a href="#" onclick="window.print();return false;">click here</a> to print, then select "Save as PDF". The maximum of 5,000 results can take 10+ minutes for the PDF to generate.</p>
    </div>

    <div class="header">
        <h1>VigiLit Search Results Print Preview</h1>
        <p>Generated on @DateTime.UtcNow.ToString("d MMM yyyy") at @DateTime.UtcNow.ToString("HH:mm") UTC by @User.Identity.Name</p>
    </div>

    <div class="filters">
        <h2>Search Filters</h2>
        @if (!string.IsNullOrWhiteSpace(Model.Filters.Term))
        {
            FiltersVisible = true;
            <p><b>ID</b> @Model.Filters.Term</p>
        }
        @if (Model.Filters.CreatedFrom.HasValue && Model.Filters.CreatedTo.HasValue)
        {
            FiltersVisible = true;
            <p><b>Created</b> @Model.Filters.CreatedFrom?.ToString("d MMM yyyy") - @Model.Filters.CreatedTo?.ToString("d MMM yyyy")</p>
        }
        @if (Model.Filters.CreatedFrom.HasValue && !Model.Filters.CreatedTo.HasValue)
        {
            FiltersVisible = true;
            <p><b>Created From</b> @Model.Filters.CreatedFrom?.ToString("d MMM yyyy")</p>
        }
        @if (Model.Filters.CreatedTo.HasValue && !Model.Filters.CreatedFrom.HasValue)
        {
            FiltersVisible = true;
            <p><b>Created To</b> @Model.Filters.CreatedTo?.ToString("d MMM yyyy")</p>
        }
        @if (Model.Filters.LastUpdatedFrom.HasValue && Model.Filters.LastUpdatedTo.HasValue)
        {
            FiltersVisible = true;
            <p><b>Last Updated</b> @Model.Filters.LastUpdatedFrom?.ToString("d MMM yyyy") - @Model.Filters.LastUpdatedTo?.ToString("d MMM yyyy")</p>
        }
        @if (Model.Filters.LastUpdatedFrom.HasValue && !Model.Filters.LastUpdatedTo.HasValue)
        {
            FiltersVisible = true;
            <p><b>Last Updated From</b> @Model.Filters.LastUpdatedFrom?.ToString("d MMM yyyy")</p>
        }
        @if (Model.Filters.LastUpdatedTo.HasValue && !Model.Filters.LastUpdatedFrom.HasValue)
        {
            FiltersVisible = true;
            <p><b>Last Updated To</b> @Model.Filters.LastUpdatedTo?.ToString("d MMM yyyy")</p>
        }
        @if (!string.IsNullOrWhiteSpace(Model.Filters.PSUR))
        {
            FiltersVisible = true;
            <p><b>PSUR Relevant?</b> @Model.Filters.PSUR</p>
        }
        @if (!string.IsNullOrWhiteSpace(Model.Filters.Company))
        {
            FiltersVisible = true;
            <p><b>Company</b> @Model.Filters.Company</p>
        }
        @if (!string.IsNullOrWhiteSpace(Model.Filters.Title))
        {
            FiltersVisible = true;
            <p><b>Title</b> @Model.Filters.Title</p>
        }
        @if (!string.IsNullOrWhiteSpace(Model.Filters.MeshTerms))
        {
            FiltersVisible = true;
            <p><b>MeSH Terms</b> @Model.Filters.MeshTerms (@(Model.Filters.MeshTermsOr ? "OR" : "AND"))</p>
        }
        @if (!string.IsNullOrWhiteSpace(Model.Filters.Keywords))
        {
            FiltersVisible = true;
            <p><b>Keywords</b> @Model.Filters.Keywords (@(Model.Filters.KeywordsOr ? "OR" : "AND"))</p>
        }
        @if (Model.Filters.Categories.Any())
        {
            FiltersVisible = true;
            <div>
                <div><b>Categories</b></div>
                <div>
                    <div>
                        @foreach (var category in Model.Filters.Categories)
                        {
                            <div>@category</div>
                        }
                    </div>
                </div>
            </div>
        }
        @if (Model.Filters.Substances.Any())
        {
            FiltersVisible = true;
            <div>
                <div><b>Substances</b></div>
                <div>
                    <div>
                        @foreach (var substance in Model.Filters.Substances)
                        {
                            <div>@substance</div>
                        }
                    </div>
                </div>
            </div>
        }
        @if (!FiltersVisible)
        {
            <p>None</p>
        }
    </div>

    <div class="results">
        <h2>Search Results</h2>
    </div>
    @if (Model.Classifications.Any())
    {
        <div class="results">
            <p>Number of results: @Model.Classifications.Count().ToString("N0")</p>
        </div>

        <div class="classifications">
                @foreach (var classification in Model.Classifications)
            {
                <div>
                    <div>
                        <div>
                            @if (!string.IsNullOrWhiteSpace(classification.Title))
                            {
                                <h3>@Html.Raw(AntiXss.SanitizeHtml(classification.Title))</h3>
                            }
                            else
                            {
                                <h3>No information available.</h3>
                            }
                            @if (!string.IsNullOrWhiteSpace(classification.Abstract))
                            {
                                <p><b>@classification.SourceTextLabel</b><br />@Html.Raw(AntiXss.SanitizeHtml(classification.Abstract))</p>
                            }
                            else
                            {
                                <p><b>@classification.SourceTextLabel</b><br />No information available.</p>
                            }
                            @if (!string.IsNullOrWhiteSpace(classification.Authors))
                            {
                                <p><b>Author</b><br />@classification.Authors</p>
                            }
                            else
                            {
                                <p><b>Author</b><br />No information available.</p>
                            }
                            @if (!string.IsNullOrWhiteSpace(classification.MeshTerms))
                            {
                                <p><b>Mesh Term</b><br />@classification.MeshTerms</p>
                            }
                            else
                            {
                                <p><b>Mesh Term</b><br />No information available.</p>
                            }
                            @if (!string.IsNullOrWhiteSpace(classification.Keywords))
                            {
                                <p><b>Keywords</b><br />@classification.Keywords</p>
                            }
                            else
                            {
                                <p><b>Keywords</b><br />No information available.</p>
                            }
                            @if (!string.IsNullOrWhiteSpace(classification.AffiliationTextFirstAuthor))
                            {
                                <p><b>Affiliation</b><br />@classification.AffiliationTextFirstAuthor</p>
                            }
                            else
                            {
                                <p><b>Affiliation</b><br />No information available.</p>
                            }
                        </div>
                        <div>
                            <h3>@classification.Substance</h3>
                            @if (!string.IsNullOrWhiteSpace(classification.ClassificationCategory))
                            {
                                <p><b>Classification Category</b><br />@classification.ClassificationCategory</p>
                            }
                            else
                            {
                                <p><b>Classification Category</b><br/>N/A</p>
                            }

                            @if (classification.SpecialSituationList.Count > 0)
                            {
                                <p class="safetySpecificParagraph">
                                        <b>Special Situation</b><br />

                                    <div class="safetySpecificItems">

                                            @foreach (var item in classification.SpecialSituationList)
                                        {
                                            @item
                                            <br/>
                                        }
                                    </div>
                                </p>
                            }
                            @if (!string.IsNullOrWhiteSpace(classification.MinimalCriteria))
                            {
                                <p><b>Minimal Criteria</b><br />@classification.MinimalCriteria</p>
                            }
                            else
                            {
                                <p><b>Minimal Criteria</b><br />N/A</p>
                            }
                            @if (!string.IsNullOrWhiteSpace(classification.CountryOfOccurrence))
                            {
                                <p><b>Country of Occurrence</b><br />@classification.CountryOfOccurrence</p>
                            }
                            else
                            {
                                <p><b>Country of Occurrence</b><br />N/A</p>
                            }
                            @if (!string.IsNullOrWhiteSpace(classification.PSURRelevanceAbstract))
                            {
                                <p><b>PSUR Relevant</b><br />@classification.PSURRelevanceAbstract</p>
                            }
                            else
                            {
                                <p><b>PSUR Relevant</b><br />N/A</p>
                            }
                            @if (!string.IsNullOrWhiteSpace(classification.PvSafetyDatabaseId))
                            {
                                <p><b>PV Safety Database ID</b><br />@classification.PvSafetyDatabaseId</p>
                            }
                            else
                            {
                                <p><b>PV Safety Database ID</b><br />N/A</p>
                            }
                            @if (!string.IsNullOrWhiteSpace(classification.DosageForm))
                            {
                                <p><b>Dosage Form</b><br />@classification.DosageForm</p>
                            }
                            else
                            {
                                <p><b>Dosage Form</b><br/>N/A</p>
                            }
                            <p><b>Created Date</b><br />@classification.CreatedDate.ToString("d MMM yyyy")</p>
                            <p><b>Last Updated Date</b><br />@classification.LastUpdatedDate.ToString("d MMM yyyy")</p>
                        </div>
                    </div>
                    <div>
                        <p>
                            <span><b>PLX ID</b> @classification.Id</span>
                            <span><b>Source Id</b> @classification.SourceId</span>
                            @if (!string.IsNullOrWhiteSpace(classification.Doi))
                            {
                                <span><b>DOI</b> @classification.Doi</span>
                            }
                            <span><b>Modified</b>
                                @if (classification.DateRevised != DateTime.MinValue)
                                {
                                    <span> @classification.DateRevised.ToString("d MMM yyyy") ET</span>
                                }
                                else
                                {
                                    <span>N/A</span>
                                }
                            </span>
                        </p>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="results">
            <p>None</p>
        </div>
    }

</div>

</body>

</html>