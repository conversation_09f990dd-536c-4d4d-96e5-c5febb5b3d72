﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ReferenceManagement.Service;

internal class SpecialSituationRepository : TrackingGenericRepository<SpecialSituation>, ISpecialSituationRepository
{
    public SpecialSituationRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<IEnumerable<SpecialSituation>> GetAllAsync()
    {
        return await context.Set<SpecialSituation>()
            .OrderBy(x => x.Name)
            .AsNoTracking()
            .ToListAsync();
    }
}