﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using System.Text.Json;
using Console = PhlexVisionTestHarness.CrossCutting.ConsoleEx;

namespace PhlexVisionTestHarness.WebApplicationExtensions;

internal static class ErrorHandler
{
    private const string CorrelationIdHeader = "X-Correlation-ID";

    public static void AddErrorHandler(this WebApplication app)
    {
        app.MapPost("/error/VigiLit", async (HttpContext context) =>
        {
            using var reader = new StreamReader(context.Request.Body);
            var body = await reader.ReadToEndAsync();

            var visionError = JsonSerializer.Deserialize<VisionError>(body);

            var correlationId = context.Request.Headers[CorrelationIdHeader];

            Console.WriteLine(ConsoleColor.Red, $"Received Error callback {correlationId}:\n{visionError?.ErrorType}");
            return Results.Ok("Callback Error received");
        });
    }
}