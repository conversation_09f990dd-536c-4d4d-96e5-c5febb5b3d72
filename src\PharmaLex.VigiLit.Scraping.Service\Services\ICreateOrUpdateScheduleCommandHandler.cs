﻿using PharmaLex.VigiLit.Scraping.Client;

namespace PharmaLex.VigiLit.Scraping.Service.Services
{
    public interface ICreateOrUpdateScheduleCommandHandler
    {
        /// <summary>
        /// Consumes the specified command to schedule a web crawl using a list of URLs
        /// </summary>
        /// <param name="command">The command.</param>
        Task Consume(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default);
    }
}
