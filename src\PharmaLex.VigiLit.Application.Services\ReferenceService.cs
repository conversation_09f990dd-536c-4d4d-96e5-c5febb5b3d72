using AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using System.Text.RegularExpressions;

namespace PharmaLex.VigiLit.Application.Services;

public class ReferenceService : IReferenceService
{
    private readonly IReferenceRepository _referenceRepository;
    private readonly IMapper _mapper;
    private readonly IReferenceClassificationRepository _referenceClassificationRepository;
    private readonly ISubstanceRepository _substanceRepository;
    private readonly ISpecialSituationService _specialSituationFieldService;

    public ReferenceService(IReferenceRepository referenceRepository,
        IMapper mapper,
        IReferenceClassificationRepository referenceClassificationRepository,
        ISubstanceRepository substanceRepository,
        ISpecialSituationService specialSituationFieldService)
    {
        _mapper = mapper;
        _referenceRepository = referenceRepository;
        _referenceClassificationRepository = referenceClassificationRepository;
        _substanceRepository = substanceRepository;
        _specialSituationFieldService = specialSituationFieldService;
    }

    public async Task<IEnumerable<ReferenceModel>> GetAllAsync()
    {
        var reference = await _referenceRepository.GetAllAsync();
        return reference;
    }

    public async Task<ReferenceDetailedModel> GetReferenceByIdAsync(int id)
    {
        var reference = await _referenceRepository.GetReferenceById(id);
        return _mapper.Map<ReferenceDetailedModel>(reference);
    }

    public async Task<ReferenceDetailsPageModel> GetReferenceDetails(int referenceId, User user)
    {
        var model = new ReferenceDetailsPageModel()
        {
            Reference = await GetReferenceByIdAsync(referenceId),
            ReferenceClassifications = await _referenceClassificationRepository.GetForReferenceDetails(referenceId, user)
        };

        return model;
    }

    public async Task<ReferenceSplitPageModel> GetSplitReferenceDetails(int referenceId)
    {
        var reference = await GetReferenceByIdAsync(referenceId);

        var model = new ReferenceSplitPageModel();

        if (reference != null)
        {
            var substances = await _substanceRepository.GetActiveSubstancesWithNoClassificationsForReference(referenceId);
            var specialSituations = await _specialSituationFieldService.GetAllAsync();

            model = new ReferenceSplitPageModel()
            {
                Reference = reference,

                Substances = substances.OrderBy(x => x.Name).ToList(),

                ReferenceClassification = new ReferenceSplitClassificationModel()
                {
                    ReferenceId = referenceId,
                    ClassificationCategoryId = (int)ClassificationCategoriesType.PotentialCase,
                    CountryOfOccurrence = reference.CountryOfOccurrence,
                    MinimalCriteria = "",
                    DosageForm = ""
                },

                SpecialSituationList = specialSituations
            };
        }

        return model;
    }
    public async Task<bool> IsDoiExisting(string doiId)
    {
        return await _referenceRepository.DoesDoiExist(doiId);
    }

    public Task<bool> IsDoiValid(string doi)
    {
        string doiPattern = @"^10\.\d{4,9}/[-._;()/:A-Z0-9]+$";
        var regex = new Regex(doiPattern, RegexOptions.IgnoreCase, TimeSpan.FromSeconds(10));
        bool result = regex.IsMatch(doi);
        return Task.FromResult(result);
    }
}