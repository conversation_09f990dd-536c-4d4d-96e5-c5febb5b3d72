﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Search.Ui.Models;

namespace PharmaLex.VigiLit.Search.Ui;
public class PrintPreviewSearchResultModelMappingProfile : Profile
{
    public PrintPreviewSearchResultModelMappingProfile()
    {
        CreateMap<ReferenceClassification, PrintPreviewSearchResultModel>()
            .ForMember(d => d.Id, s => s.MapFrom(x => x.Id))
            .ForMember(d => d.SourceId, s => s.MapFrom(x => x.Reference.SourceId))
            .ForMember(d => d.Doi, s => s.MapFrom(x => x.Reference.Doi))
            .ForMember(d => d.DateRevised, s => s.MapFrom(x => x.Reference.DateRevised))
            .ForMember(d => d.Title, s => s.MapFrom(x => x.Reference.Title))
            .ForMember(d => d.Abstract, s => s.MapFrom(x => x.Reference.Abstract))
            .ForMember(d => d.Authors, s => s.MapFrom(x => x.Reference.Authors))
            .ForMember(d => d.MeshTerms, s => s.MapFrom(x => x.Reference.MeshHeadings))
            .ForMember(d => d.Keywords, s => s.MapFrom(x => x.Reference.Keywords))
            .ForMember(d => d.AffiliationTextFirstAuthor, s => s.MapFrom(x => x.Reference.AffiliationTextFirstAuthor))
            .ForMember(d => d.ClassificationCategory, s => s.MapFrom(x => x.ClassificationCategory.Name))
            .ForMember(d => d.Substance, s => s.MapFrom(x => x.Substance.Name))
            .ForMember(d => d.SourceTextLabel, s => s.MapFrom(rc => (SourceSystem)rc.Reference.SourceSystem == SourceSystem.PubMed ? "Abstract" : "Match"))
            .ForMember(d => d.CreatedDate, s => s.MapFrom(x => x.CreatedDate))
            .ForMember(d => d.LastUpdatedDate, s => s.MapFrom(x => x.LastUpdatedDate));
    }
}
