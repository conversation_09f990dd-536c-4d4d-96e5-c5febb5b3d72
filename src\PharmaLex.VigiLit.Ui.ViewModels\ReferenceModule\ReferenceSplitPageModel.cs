﻿using System.Collections.Generic;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class ReferenceSplitPageModel
{
    public ReferenceDetailedModel Reference { get; set; }
    public IEnumerable<SubstanceItemModel> Substances { get; set; }
    public List<CompanyModel> Companies { get; set; }
    public ReferenceSplitClassificationModel ReferenceClassification { get; set; }
    public IEnumerable<SpecialSituationModel> SpecialSituationList { get; set; }
}
