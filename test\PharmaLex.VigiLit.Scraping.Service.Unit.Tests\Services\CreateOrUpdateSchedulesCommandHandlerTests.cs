﻿using Apify.SDK.Model;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Helpers;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Services;
using Phlex.Core.Apify.Interfaces;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Services;

public class CreateOrUpdateScheduleCommandHandlerTests
{
    private readonly Mock<ILogger<CreateOrUpdateScheduleCommandHandler>> _mockLogger;
    private readonly Mock<IApifyTaskService> _mockTaskService;
    private readonly Mock<IApifyScheduleService> _mockScheduleService;
    private readonly Mock<IApifyWebhookService> _mockWebhookService;
    private readonly Mock<IScrapingConfigurationService> _mockConfigService;
    private readonly CreateOrUpdateScheduleCommandHandler _handler;

    private const string TestWebhookUrl = "https://example.com/webhook";
    private const string TestTaskId = "test-task-123";
    private const string TestScheduleId = "test-schedule-456";

    public CreateOrUpdateScheduleCommandHandlerTests()
    {
        _mockLogger = new Mock<ILogger<CreateOrUpdateScheduleCommandHandler>>();
        _mockTaskService = new Mock<IApifyTaskService>();
        _mockScheduleService = new Mock<IApifyScheduleService>();
        _mockWebhookService = new Mock<IApifyWebhookService>();
        _mockConfigService = new Mock<IScrapingConfigurationService>();

        _mockConfigService.Setup(x => x.GetWebhookUrl()).Returns(TestWebhookUrl);
        _mockTaskService.Setup(x => x.CreateTaskForJournalAsync(It.IsAny<JournalScheduleInfo>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(TestTaskId);

        _handler = new CreateOrUpdateScheduleCommandHandler(
            _mockLogger.Object,
            Mock.Of<IApifyClient>(),
            _mockTaskService.Object,
            _mockScheduleService.Object,
            _mockWebhookService.Object,
            _mockConfigService.Object);
    }

    [Fact]
    public async Task Consume_CreateOperation_CreatesNewScheduleAndWebhook()
    {
        // Arrange
        var command = CreateTestCommand(JournalOperationType.Create);

        var items = new List<GetListOfSchedulesResponseDataItems>
        {
            new(
                id: Fake.GetRandomString(10),
                userId: "test-user-1",
                name: "test-schedule",
                createdAt: DateTime.UtcNow.ToString("o"),
                modifiedAt: DateTime.UtcNow.ToString("o"),
                nextRunAt: DateTime.UtcNow.AddHours(1).ToString("o"),
                isEnabled: true,
                isExclusive: false,
                cronExpression: Fake.GetRandomString(10),
                timezone: "UTC",
                actions:
                [
                    new(
                        id: "action-1",
                        type: "RUN_ACTOR",
                        actorTaskId: "actor-1")
                ])
        };
        var data = new GetListOfSchedulesResponseData(0, 0, 0, false, 0, items);
        var scheduleResponse = new GetListOfSchedulesResponse(data);

        _mockScheduleService.Setup(x => x.GetSchedulesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(scheduleResponse);

        _mockScheduleService.Setup(x => x.CreateScheduleForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _handler.Consume(command);

        // Assert
        _mockTaskService.Verify(x => x.CreateTaskForJournalAsync(
            It.Is<JournalScheduleInfo>(j => j.Name == command.Journal.Name),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);

        _mockScheduleService.Verify(x => x.CreateScheduleForTaskAsync(
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);

        _mockWebhookService.Verify(x => x.CreateWebhookForTaskAsync(
            TestTaskId,
            TestWebhookUrl,
            It.IsAny<CancellationToken>()), Times.Once);

        VerifyLogCalled(LogLevel.Information, "CreateOrUpdateScheduleCommandHandler:Consume:");
    }

    [Fact]
    public async Task Consume_CreateOperation_WithExistingSchedule_UpdatesSchedule()
    {
        // Arrange
        var command = CreateTestCommand(JournalOperationType.Create);
        var existingSchedule = new GetListOfSchedulesResponseDataItems(
                id: TestScheduleId,
                userId: "test-user-1",
                name: "test-schedule",
                createdAt: DateTime.UtcNow.ToString("o"),
                modifiedAt: DateTime.UtcNow.ToString("o"),
                nextRunAt: DateTime.UtcNow.AddHours(1).ToString("o"),
                isEnabled: true,
                isExclusive: false,
                cronExpression: command.Journal.CronExpression,
                timezone: "UTC",
                actions:
                [
                    new(
                        id: "action-1",
                        type: "RUN_ACTOR",
                        actorTaskId: "actor-1")
                ]);

        var items = new List<GetListOfSchedulesResponseDataItems>
        {
            existingSchedule
        };

        var data = new GetListOfSchedulesResponseData(0, 0, 0, false, 0, items);
        var scheduleResponse = new GetListOfSchedulesResponse(data);

        _mockScheduleService.Setup(x => x.GetSchedulesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(scheduleResponse);

        // Act
        await _handler.Consume(command);

        // Assert
        _mockScheduleService.Verify(x => x.UpdateScheduleAsync(
            TestScheduleId,
            TestTaskId,
            It.IsAny<CancellationToken>()), Times.Once);

        _mockScheduleService.Verify(x => x.CreateScheduleForTaskAsync(
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Never);

        _mockWebhookService.Verify(x => x.CreateWebhookForTaskAsync(
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Never);

        VerifyLogCalled(LogLevel.Information, "CreateOrUpdateScheduleCommandHandler:Consume:");
    }

    [Fact]
    public async Task Consume_CreateOperation_WithTaskCreationFailure_LogsWarning()
    {
        // Arrange
        var command = CreateTestCommand(JournalOperationType.Create);
        _mockTaskService.Setup(x => x.CreateTaskForJournalAsync(It.IsAny<JournalScheduleInfo>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(string.Empty);

        // Act
        await _handler.Consume(command);

        // Assert
        _mockScheduleService.Verify(x => x.CreateScheduleForTaskAsync(
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Consume_CreateOperation_WithMissingWebhookUrl_LogsWarning()
    {
        // Arrange
        var command = CreateTestCommand(JournalOperationType.Create);
        _mockConfigService.Setup(x => x.GetWebhookUrl()).Returns(string.Empty);
        var items = new List<GetListOfSchedulesResponseDataItems>
        {
            new(
                id: Fake.GetRandomString(10),
                userId: "test-user-1",
                name: "test-schedule",
                createdAt: DateTime.UtcNow.ToString("o"),
                modifiedAt: DateTime.UtcNow.ToString("o"),
                nextRunAt: DateTime.UtcNow.AddHours(1).ToString("o"),
                isEnabled: true,
                isExclusive: false,
                cronExpression: Fake.GetRandomString(10),
                timezone: "UTC",
                actions:
                [
                    new(
                        id: "action-1",
                        type: "RUN_ACTOR",
                        actorTaskId: "actor-1")
                ])
        };
        var data = new GetListOfSchedulesResponseData(0, 0, 0, false, 0, items);
        var scheduleResponse = new GetListOfSchedulesResponse(data);

        _mockScheduleService.Setup(x => x.GetSchedulesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(scheduleResponse);

        // Act
        await _handler.Consume(command);

        // Assert
        _mockWebhookService.Verify(x => x.CreateWebhookForTaskAsync(
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Consume_WithException_ThrowsInvalidOperationException()
    {
        // Arrange
        var command = CreateTestCommand(JournalOperationType.Create);
        _mockTaskService.Setup(x => x.CreateTaskForJournalAsync(It.IsAny<JournalScheduleInfo>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        // Act & Assert
        var ex = await Assert.ThrowsAsync<InvalidOperationException>(() => _handler.Consume(command));
        Assert.Equal("Failed to create or update Apify schedule: Test exception", ex.Message);

        VerifyLogCalled(LogLevel.Error, "Error during create or update schedule");
    }

    [Fact]
    public void CreateSchedule_GeneratesCorrectTaskName()
    {
        // Arrange
        var command = CreateTestCommand(JournalOperationType.Create);
        var expectedTaskName = "vigilit-test-journal-123";

        // Act
        var taskName = $"vigilit-{ScrapingHelper.SanitizeApifyName(command.Journal.Name)}-123";

        // Assert
        Assert.Equal(expectedTaskName, taskName);
    }

    private static CreateOrUpdateScheduleCommand CreateTestCommand(JournalOperationType operationType)
    {
        return new CreateOrUpdateScheduleCommand
        {
            OperationType = operationType,
            Journal = new Domain.Models.Journal
            {
                Enabled = true,
                Name = "Test Journal",
                Url = "https://example.com",
                CronExpression = "1 1 1 1 *"
            }
        };
    }

    private void VerifyLogCalled(LogLevel level, string message)
    {
        _mockLogger.Verify(
            x => x.Log(
                level,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }
}