﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Auditing.Client;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using System.Text.Json;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision.MetaDataExtractor;
internal class MultiPromptMetaDataExtractor : IMetaDataExtractor
{
    private readonly ILogger<MultiPromptMetaDataExtractor> _logger;
    private readonly IAuditClient _auditClient;

    public MultiPromptMetaDataExtractor(ILogger<MultiPromptMetaDataExtractor> logger, IAuditClient auditClient)
    {
        _logger = logger;
        _auditClient = auditClient;
    }

    private const string PhlexVisionExtractionFailed = "PhlexVision extraction failed";

    private readonly JsonSerializerOptions _caseInsensitiveSerializerOptions = new JsonSerializerOptions
    {
        PropertyNameCaseInsensitive = true
    };

    public async Task<(ContextInfo, ExtractedMetadata?)> GetExtractedMetaData(Stream responseBodyStream, Guid correlationId)
    {
        using var reader = new StreamReader(responseBodyStream);
        var body = await reader.ReadToEndAsync();
        _logger.LogInformation("{Body}", LogSanitizer.Sanitize(body));

        var root = JsonSerializer.Deserialize<RootObjectMultiPrompt>(body, _caseInsensitiveSerializerOptions);

        if (root == null) throw new ArgumentException("Could not deserialize response body.");

        var extractedMetaData = root.AiProcessing.MetadataExtraction;

        if (extractedMetaData == null)
        {
            _logger.LogWarning("ExtractedMetadata is null for CorrelationId: {CorrelationId}", correlationId);
            await SendStatusChangedEvent(correlationId, PhlexVisionExtractionFailed);
        }

        var language = root.Languages?.Detected?.Length > 0 ? root.Languages.Detected[0] : string.Empty;

        var contextInfo = new ContextInfo()
        {
            Language = language,
            RawTranslatedText = root.TranslatedText,
        };

        return (contextInfo, extractedMetaData);
    }

    private async Task SendStatusChangedEvent(Guid correlationId, string statusMessage)
    {
        var statusChangedEvent = new StatusChangedEvent(nameof(ImportType.File), correlationId,
            DateTime.UtcNow, "<EMAIL>", statusMessage);
        await _auditClient.Send(statusChangedEvent);
    }
}
