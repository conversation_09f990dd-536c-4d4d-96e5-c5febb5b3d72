﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Console = PhlexVisionTestHarness.CrossCutting.ConsoleEx;


namespace PhlexVisionTestHarness.WebApplicationExtensions;

internal static class DocumentDownloadHandler
{
    private const string CorrelationIdHeader = "X-Correlation-ID";

    public static void AddDocumentDownloadHandler(this WebApplication app)
    {
        app.MapGet("DownloadDocument/VigiLit/SOME_FILE_NAME.PDF", async (HttpContext context) =>
        {
            var correlationId = context.Request.Headers[CorrelationIdHeader];

            Console.WriteLine(ConsoleColor.Green, $"{correlationId}");

            await Task.Delay(10);

            var bytes = File.ReadAllBytes("TestData\\TESTDATA2.PDF");
            return Results.Stream(new MemoryStream(bytes), "application/pdf", "filename.pdf");

        });
    }
}