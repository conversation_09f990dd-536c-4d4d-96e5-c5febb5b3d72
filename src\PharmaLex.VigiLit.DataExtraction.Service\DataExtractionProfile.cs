﻿using AutoMapper;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.ImportManagement.Client;
using PharmaLex.VigiLit.ImportManagement.Client.Dto;

namespace PharmaLex.VigiLit.DataExtraction.Service;

internal class DataExtractionProfile : Profile, IDataExtractionProfile
{
    private readonly List<IExtractionMatchValidator> _matchValidators = null!;

    public DataExtractionProfile(IEnumerable<IExtractionMatchValidator> matchValidators)
    {
        _matchValidators = matchValidators?.ToList() ?? new List<IExtractionMatchValidator>();

    }
    public DataExtractionProfile()
    {
        _matchValidators = new List<IExtractionMatchValidator>();
    }

    public ReferenceDto Copy(ExtractedReference source)
    {
        ArgumentNullException.ThrowIfNull(source);

        var destination = new ReferenceDto()
        {
            Abstract = source.Abstract.Value,
            AffiliationTextFirstAuthor = source.Affiliations[0].Value,
            Authors = string.Join(",", source.Authors.Select(a => a.Value).ToArray()),
            CountryOfOccurrence = source.CountryOfOccurrence.Value,
            Doi = source.Doi.Value,
            FullPagination = source.Pages.Value,
            Issn = source.Issn.Value,
            SourceSystem = (int)SourceSystem.Web,
            SourceId = string.Empty,
            PublicationType = string.Empty,
            PublicationYear = ushort.TryParse(source.Year?.Value, out var year) ? year : null,
            Title = source.Title.Value,
            Volume = source.Volume.Value,
            VolumeAbbreviation = string.Empty,
            Keywords = source.Keywords.Value,
            MeshHeadings = string.Empty,
            JournalTitle = source.JournalTitle.Value,
        };
        return destination;
    }
    public FailedImportReferenceDto CopyFailed(ExtractedReference source, float minMandatoryConfidenceLevel)
    {
        ArgumentNullException.ThrowIfNull(source);

        var countryMatchChecker = _matchValidators.OfType<CountryOfOccurrenceChecker>().FirstOrDefault();
        var countryMatchResult = countryMatchChecker?.GetMatchResult(source);

        var journalTitleMatchChecker = _matchValidators.OfType<JournalTitleChecker>().FirstOrDefault();
        var journalTitleMatchResult = journalTitleMatchChecker?.GetMatchResult(source);

        var destination = new FailedImportReferenceDto()
        {
            Abstract = source.Abstract.Value,
            AffiliationTextFirstAuthor = source.Affiliations[0].Value,
            Authors = string.Join(",", source.Authors.Select(a => a.Value).ToArray()),
            CountryOfOccurrence = source.CountryOfOccurrence.Value,
            Doi = source.Doi.Value,
            FullPagination = source.Pages.Value,
            Issn = source.Issn.Value,
            SourceSystem = (int)SourceSystem.ManualCorrection,
            SourceId = string.Empty,
            PublicationType = string.Empty,
            PublicationYear = ushort.TryParse(source.Year?.Value, out var year) ? year : null,
            Title = source.Title.Value,
            Volume = source.Volume.Value,
            VolumeAbbreviation = string.Empty,
            Keywords = source.Keywords.Value,
            MeshHeadings = string.Empty,
            JournalTitle = source.JournalTitle.Value,

            AbstractConfidence = ToByteOrDefault(source.Abstract?.Confidence),
            AffiliationTextFirstAuthorConfidence = ToByteOrDefault(source.Affiliations?.FirstOrDefault()?.Confidence),
            AuthorsConfidence = ToByteOrDefault(source.Authors?.FirstOrDefault()?.Confidence),
            CountryOfOccurrenceConfidence = ToByteOrDefault(source.CountryOfOccurrence?.Confidence),
            DoiConfidence = ToByteOrDefault(source.Doi?.Confidence),
            FullPaginationConfidence = ToByteOrDefault(source.Pages?.Confidence),
            IssnConfidence = ToByteOrDefault(source.Issn?.Confidence),
            IssueConfidence = ToByteOrDefault(source.IssueNumber?.Confidence),
            PublicationYearConfidence = ToByteOrDefault(source.Year?.Confidence),
            TitleConfidence = ToByteOrDefault(source.Title?.Confidence),
            VolumeConfidence = ToByteOrDefault(source.Volume?.Confidence),
            KeywordsConfidence = ToByteOrDefault(source.Keywords?.Confidence),
            JournalTitleConfidence = ToByteOrDefault(source.JournalTitle?.Confidence),
            //DateRevisedConfidence = 0,// NOSONAR
            //LanguageConfidence = 0,// NOSONAR
            //VolumeAbbreviationConfidence = 0;// NOSONAR

            AbstractConfidenceCheckPassed = CheckIfPassesConfidence(source.Abstract, minMandatoryConfidenceLevel),
            TitleConfidenceCheckPassed = CheckIfPassesConfidence(source.Title, minMandatoryConfidenceLevel),
            JournalTitleConfidenceCheckPassed = CheckIfPassesConfidence(source.JournalTitle, minMandatoryConfidenceLevel),
            CountryOfOccurrenceConfidenceCheckPassed = CheckIfPassesConfidence(source.CountryOfOccurrence, minMandatoryConfidenceLevel),
            JournalTitleMatched = journalTitleMatchResult?.MatchedValue,
            JournalTitleMatchingStatus = journalTitleMatchResult?.Status ?? MatchingStatus.Failed,
            CountryMatched = countryMatchResult?.MatchedValue,
            CountryOfOccurrenceMatchingStatus = countryMatchResult?.Status ?? MatchingStatus.Failed
        };
        return destination;
    }

    private static byte ToByteOrDefault(float? input) =>
      input.HasValue ? (byte)(input.Value * 100) : (byte)0;

    public static bool CheckIfPassesConfidence(IExtractedProperty? property, float threshold)
    {
        return property != null && property.Confidence >= threshold;
    }

}