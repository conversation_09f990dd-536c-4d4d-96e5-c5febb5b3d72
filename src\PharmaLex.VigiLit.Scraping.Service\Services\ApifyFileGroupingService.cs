using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ApifyFileGroupingService : IApifyFileGroupingService
{
    private readonly ILogger<ApifyFileGroupingService> _logger;
    private readonly IApifyClient _apifyClient;

    public ApifyFileGroupingService(ILogger<ApifyFileGroupingService> logger, IApifyClient apifyClient)
    {
        _logger = logger;
        _apifyClient = apifyClient;
    }

    public async Task<Dictionary<string, List<string>>> GroupFilesByJournalUsingKeyValueStoreAsync(string runId, CancellationToken cancellationToken = default)
    {
        var filesByJournal = new Dictionary<string, List<string>>();

        try
        {
            _logger.LogInformation("Getting key-value store file info for run ID: {RunId}", runId);

            var keyValueStoreFiles = await _apifyClient.GetKeyValueStoreFileInfoAsync(runId, cancellationToken);

            if (keyValueStoreFiles == null || keyValueStoreFiles.Count <= 0)
            {
                _logger.LogWarning("No key-value store files found for run ID: {RunId}", runId);
                return filesByJournal;
            }

            foreach (var fileInfo in keyValueStoreFiles)
            {
                try
                {
                    var journalKey = GetJournalKeyFromFileInfo(fileInfo);
                    var fileName = fileInfo.FileName ?? "unknown";

                    if (!filesByJournal.TryGetValue(journalKey, out List<string>? value))
                    {
                        value = [];
                        filesByJournal[journalKey] = value;
                    }

                    value.Add(fileName);

                    _logger.LogDebug("Associated file {FileName} with journal group {JournalKey}", fileName, journalKey);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process key-value store file info for file {FileName}, adding to default group", fileInfo.FileName);

                    const string defaultKey = "Default";
                    if (!filesByJournal.TryGetValue(defaultKey, out List<string>? value))
                    {
                        value = [];
                        filesByJournal[defaultKey] = value;
                    }

                    value.Add(fileInfo.FileName ?? "unknown");
                }
            }

            if (filesByJournal.Count == 0)
            {
                _logger.LogWarning("No files were successfully grouped for run ID: {RunId}, creating default group", runId);
                filesByJournal["Default"] = keyValueStoreFiles.Select(f => f.FileName ?? "unknown").ToList();
            }

            _logger.LogInformation("Successfully grouped {FileCount} files into {GroupCount} journal groups for run ID: {RunId}",
                keyValueStoreFiles.Count, filesByJournal.Count, runId);

            return filesByJournal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while grouping files by journal for run ID: {RunId}", runId);
            return filesByJournal;
        }
    }

    private static string ExtractHostFromUrl(string url)
    {
        try
        {
            var uri = new Uri(url);
            return uri.Host;
        }
        catch
        {
            return url.Replace("://", "_").Replace("/", "_").Replace(".", "_");
        }
    }

    private static string GetJournalKeyFromFileInfo(KeyValueStoreFileInfo fileInfo)
    {
        try
        {
            if (!string.IsNullOrWhiteSpace(fileInfo.StartUrl))
            {
                var host = ExtractHostFromUrl(fileInfo.StartUrl);
                return $"URL_{host}";
            }

            if (!string.IsNullOrWhiteSpace(fileInfo.Url))
            {
                var host = ExtractHostFromUrl(fileInfo.Url);
                return $"URL_{host}";
            }

            var fileName = fileInfo.FileName ?? "";
            if (!string.IsNullOrEmpty(fileName) && fileName.Contains("journal_"))
            {
                var parts = fileName.Split('_');
                if (parts.Length >= 2)
                {
                    return $"Journal_{parts[1]}";
                }
            }

            return $"Type_{fileInfo.Type}";
        }
        catch (Exception)
        {
            return "Default";
        }
    }
}
