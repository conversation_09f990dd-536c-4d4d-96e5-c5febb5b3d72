﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Search.Ui.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;

namespace PharmaLex.VigiLit.Search.Ui.Unit.Tests;

public class PrintPreviewSearchResultModelMappingProfileTests
{
    readonly IMapper _mapper;

    public PrintPreviewSearchResultModelMappingProfileTests()
    {
        var config = new MapperConfiguration(cfg => cfg.AddProfile<PrintPreviewSearchResultModelMappingProfile>());
        _mapper = config.CreateMapper();
    }

    [Fact]
    public void Maps_ReferenceClassification_to_PrintPreviewSearchResultModel()
    {
        // Arrange
        var referenceClassification = new ReferenceClassification(1, 2)
        {
            Reference = new Reference()
            {
                SourceId = "1",
                Doi = "my doi",
                DateRevised = new DateTime(2024, 03, 06, 0, 0, 0, DateTimeKind.Utc),
                Title = "my title",
                Abstract = "my abstract",
                Authors = "my authors",
                MeshHeadings = "my mesh headings",
                Keywords = "my keywords",
                AffiliationTextFirstAuthor = "my affiliation text first author"
            },

            ClassificationCategory = new FakeClassificationCategory("my classification category", false),
            Substance = new Substance("my substance", "type"),

            MinimalCriteria = "my minimal criteria",
            CountryOfOccurrence = "my country of occurrence",
            PSURRelevanceAbstract = PSURRelevanceAbstract.Yes,
            PvSafetyDatabaseId = "my pv safety database id",
            DosageForm = "my dosage form",

            CreatedDate = new DateTime(2024, 03, 01, 0, 0, 0, DateTimeKind.Utc),
            LastUpdatedDate = new DateTime(2024, 03, 02, 0, 0, 0, DateTimeKind.Utc)
        };

        // Act
        var model = _mapper.Map<PrintPreviewSearchResultModel>(referenceClassification);

        // Assert
        Assert.Equal("1", model.SourceId);
        Assert.Equal("my doi", model.Doi);
        Assert.Equal(new DateTime(2024, 03, 06, 0, 0, 0, DateTimeKind.Utc), model.DateRevised);
        Assert.Equal("my title", model.Title);
        Assert.Equal("my abstract", model.Abstract);
        Assert.Equal("my authors", model.Authors);
        Assert.Equal("my mesh headings", model.MeshTerms);
        Assert.Equal("my keywords", model.Keywords);
        Assert.Equal("my affiliation text first author", model.AffiliationTextFirstAuthor);

        Assert.Equal("my classification category", model.ClassificationCategory);
        Assert.Equal("my substance", model.Substance);

        Assert.Equal("my minimal criteria", model.MinimalCriteria);
        Assert.Equal("my country of occurrence", model.CountryOfOccurrence);
        Assert.Equal(PSURRelevanceAbstract.Yes.ToString(), model.PSURRelevanceAbstract);
        Assert.Equal("my pv safety database id", model.PvSafetyDatabaseId);
        Assert.Equal("my dosage form", model.DosageForm);

        Assert.Equal(new DateTime(2024, 03, 01, 0, 0, 0, DateTimeKind.Utc), model.CreatedDate);
        Assert.Equal(new DateTime(2024, 03, 02, 0, 0, 0, DateTimeKind.Utc), model.LastUpdatedDate);
    }
}
