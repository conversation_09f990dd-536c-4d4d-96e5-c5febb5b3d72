﻿using AutoMapper;
using Moq;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Ui.Generics;
using PharmaLex.VigiLit.ImportManagement.Ui.ManualEntry;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.ImportManagement.Ui.Services;
using PharmaLex.VigiLit.Test.Fakes.Entities;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Unit.Tests;

public class ManualEntryServiceTests
{
    private readonly GenericCardService<IGenericCardRepository<ImportManualEntry>, ImportManualEntry, ManualEntryModel> _manualEntryService;

    private readonly Mock<IGenericCardRepository<ImportManualEntry>> _manualEntryRepository = new();
    private readonly Mock<IImportReferenceRepository> _importReferenceRepository = new();
    private readonly Mock<IImportService> _importService = new();



    public ManualEntryServiceTests()
    {

        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ImportReferenceMappingProfile());
            mc.AddProfile(new ImportFileMappingProfile());
            mc.AddProfile(new ManualEntryMappingProfile());
        });

        var mapper = mapperConfig.CreateMapper();

        _manualEntryService = new ManualEntryService(
            _manualEntryRepository.Object,
            _importReferenceRepository.Object,
            mapper,
            _importService.Object);
    }

    [Fact]
    public async Task GetManualEntryReference_returns_ManualEntry_WhenExists()
    {
        // Arrange
        var importReference = new FakeImportManualEntry(1)
        {
            Abstract = "abstract",
            Title = "title",
            JournalTitle = "journalTitle"
        };

        _manualEntryRepository.Setup(x => x.GetById(1)).ReturnsAsync(importReference);

        // Act
        var result = await _manualEntryService.Get(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.Id);
        Assert.Equal("title", result.Title);
        Assert.Equal("abstract", result.Abstract);
        Assert.Equal("journalTitle", result.JournalTitle);

        _manualEntryRepository.Verify(repo => repo.GetById(result.Id), Times.Once);
    }

    [Fact]
    public async Task GetManualEntryReference_ShouldReturnNull_WhenManualEntryDoesNotExist()
    {
        // Arrange
        int id = 1;
        _manualEntryRepository.Setup(x => x.GetById(1)).ReturnsAsync(null as ImportManualEntry);

        // Act
        var result = await _manualEntryService.Get(id);

        // Assert
        Assert.Null(result);
        _manualEntryRepository.Verify(repo => repo.GetById(id), Times.Once);
    }

    [Fact]
    public async Task AddManualEntry_Adds_ManualEntry()
    {
        // Arrange
        var model = new ManualEntryModel
        {
            Id = 1,
            Abstract = "abstract",
            Title = "title",
            JournalTitle = "journalTitle",

        };
        _manualEntryRepository.Setup(x => x.Add(It.IsAny<ImportManualEntry>()));
        _manualEntryRepository.Setup(x => x.SaveChangesAsync());

        // Act
        await _manualEntryService.Add(model);

        // Assert
        _manualEntryRepository.Verify(x => x.Add(It.Is<ImportManualEntry>(x =>
            x.Abstract == model.Abstract
            && x.Title == model.Title
            && x.JournalTitle == model.JournalTitle
        )), Times.Once());
        _manualEntryRepository.Verify(repo => repo.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task UpdateManualEntry_Updates_ManualEntry()
    {
        // Arrange
        var importManualEntry = new FakeImportManualEntry(1)
        {
            Abstract = "abstract",
            Title = "title",
            JournalTitle = "journalTitle"
        };
        var model = new ManualEntryModel()
        {
            Id = 1,
            Abstract = "updatedAbstract",
            Title = "title",
            JournalTitle = "journalTitle",
        };
        _manualEntryRepository.Setup(x => x.GetById(1)).ReturnsAsync(importManualEntry);
        // Act
        await _manualEntryService.Update(model);

        // Assert
        _manualEntryRepository.Verify(repo => repo.SaveChangesAsync(), Times.Once);
    }


    [Fact]
    public async Task AbandonManualEntry_Deletes_ManualEntry()
    {
        // Arrange
        var id = 1;
        var manualEntry = new FakeImportManualEntry(1)
        {
            Abstract = "abstract",
            Title = "title",
            JournalTitle = "journalTitle"
        };
        _manualEntryRepository.Setup(x => x.GetById(1)).ReturnsAsync(manualEntry);
        // Act
        await _manualEntryService.Abandon(id);

        // Assert
        _manualEntryRepository.Verify(repo => repo.GetById(id), Times.Once);
        _manualEntryRepository.Verify(repo => repo.Remove(manualEntry), Times.Once);
        _manualEntryRepository.Verify(repo => repo.SaveChangesAsync(), Times.Once);
    }
}

