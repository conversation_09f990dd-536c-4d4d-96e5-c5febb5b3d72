﻿using PharmaLex.DataAccess;
using Microsoft.EntityFrameworkCore;
using PharmaLex.VigiLit.Domain.Models;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class SpecialSituationConfiguration : EntityBaseMap<SpecialSituation>
{
    public override void Configure(EntityTypeBuilder<SpecialSituation> builder)
    {
        base.Configure(builder);

        builder.ToTable("SpecialSituations");

        builder.Property(e => e.Name).IsRequired().HasMaxLength(500);
    }
}