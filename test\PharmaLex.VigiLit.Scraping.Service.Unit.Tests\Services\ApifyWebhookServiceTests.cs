using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Scraping.Service.Services;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Services;

public class ApifyWebhookServiceTests
{
    private readonly Mock<IApifyClient> _apifyClientMock;
    private readonly Mock<ILogger<ApifyWebhookService>> _loggerMock;
    private readonly ApifyWebhookService _sut;

    public ApifyWebhookServiceTests()
    {
        _apifyClientMock = new Mock<IApifyClient>();
        _loggerMock = new Mock<ILogger<ApifyWebhookService>>();
        _sut = new ApifyWebhookService(_apifyClientMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task CreateWebhookForTaskAsync_WhenSuccessful_CompletesWithoutException()
    {
        // Arrange
        var taskId = Fake.GetRandomString(8);
        var webhookUrl = "https://localhost:5001/webhook";

        _apifyClientMock.Setup(x => x.CreateWebhookAsync(taskId, webhookUrl, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Apify.SDK.Model.CreateWebhookResponse(new Apify.SDK.Model.Webhook(
                id: Fake.GetRandomString(10),
                createdAt: DateTime.UtcNow.ToString("O"),
                modifiedAt: DateTime.UtcNow.ToString("O"),
                userId: Fake.GetRandomString(8),
                eventTypes: [],
                condition: new Apify.SDK.Model.WebhookCondition(),
                ignoreSslErrors: false,
                doNotRetry: false,
                requestUrl: webhookUrl,
                payloadTemplate: string.Empty,
                headersTemplate: string.Empty,
                description: Fake.GetRandomString(15))));

        // Act
        await _sut.CreateWebhookForTaskAsync(taskId, webhookUrl, CancellationToken.None);

        // Assert
        _apifyClientMock.Verify(x => x.CreateWebhookAsync(webhookUrl, taskId, It.IsAny<CancellationToken>()), Times.Once);
        VerifyLogCalled(LogLevel.Information, "Created Apify webhook");
    }

    [Fact]
    public async Task CreateWebhookForTaskAsync_WhenApifyClientThrows_ThrowsInvalidOperationException()
    {
        // Arrange
        var taskId = Fake.GetRandomString(8);
        var webhookUrl = "https://localhost:5001/webhook";
        var originalException = new Exception("Apify API error");

        _apifyClientMock.Setup(x => x.CreateWebhookAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(originalException);

        // Act
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _sut.CreateWebhookForTaskAsync(taskId, webhookUrl, CancellationToken.None));

        // Assert
        Assert.Contains($"Failed to create Apify webhook for task '{taskId}' with URL '{webhookUrl}'", exception.Message);
        Assert.Equal(originalException, exception.InnerException);
        VerifyLogCalled(LogLevel.Error, "Failed to create Apify webhook");
    }

    [Theory]
    [InlineData("https://localhost:5001/webhook")]
    [InlineData("https://api.example.com/apify/webhook")]
    [InlineData("http://internal-service:8080/notifications")]
    [InlineData("https://webhook.site/unique-id")]
    public async Task CreateWebhookForTaskAsync_WithVariousWebhookUrls_CallsApifyClientCorrectly(string webhookUrl)
    {
        // Arrange
        var taskId = Fake.GetRandomString(8);

        _apifyClientMock.Setup(x => x.CreateWebhookAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Apify.SDK.Model.CreateWebhookResponse(new Apify.SDK.Model.Webhook(
                id: Fake.GetRandomString(10),
                createdAt: DateTime.UtcNow.ToString("O"),
                modifiedAt: DateTime.UtcNow.ToString("O"),
                userId: Fake.GetRandomString(8),
                eventTypes: [],
                condition: new Apify.SDK.Model.WebhookCondition(),
                ignoreSslErrors: false,
                doNotRetry: false,
                requestUrl: webhookUrl,
                payloadTemplate: string.Empty,
                headersTemplate: string.Empty,
                description: Fake.GetRandomString(15))));

        // Act
        await _sut.CreateWebhookForTaskAsync(taskId, webhookUrl, CancellationToken.None);

        // Assert
        _apifyClientMock.Verify(x => x.CreateWebhookAsync(webhookUrl, taskId, It.IsAny<CancellationToken>()), Times.Once);
        VerifyLogCalled(LogLevel.Information, $"Created Apify webhook for task '{taskId}' with URL '{webhookUrl}'");
    }

    [Fact]
    public async Task CreateWebhookForTaskAsync_WithSpecialCharactersInUrl_HandlesCorrectly()
    {
        // Arrange
        var taskId = Fake.GetRandomString(8);
        var webhookUrl = "https://api.example.com/webhook?token=abc123&format=json&callback=true";

        _apifyClientMock.Setup(x => x.CreateWebhookAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Apify.SDK.Model.CreateWebhookResponse(new Apify.SDK.Model.Webhook(
                id: Fake.GetRandomString(10),
                createdAt: DateTime.UtcNow.ToString("O"),
                modifiedAt: DateTime.UtcNow.ToString("O"),
                userId: Fake.GetRandomString(8),
                eventTypes: [],
                condition: new Apify.SDK.Model.WebhookCondition(),
                ignoreSslErrors: false,
                doNotRetry: false,
                requestUrl: webhookUrl,
                payloadTemplate: string.Empty,
                headersTemplate: string.Empty,
                description: Fake.GetRandomString(15))));

        // Act
        await _sut.CreateWebhookForTaskAsync(taskId, webhookUrl, CancellationToken.None);

        // Assert
        _apifyClientMock.Verify(x => x.CreateWebhookAsync(webhookUrl, taskId, It.IsAny<CancellationToken>()), Times.Once);
        VerifyLogCalled(LogLevel.Information, "Created Apify webhook");
    }

    private void VerifyLogCalled(LogLevel level, string message)
    {
        _loggerMock.Verify(
            x => x.Log(
                level,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }
}
