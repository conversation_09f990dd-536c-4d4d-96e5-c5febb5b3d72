﻿using FuzzySharp;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.ImportManagement.Client;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl;

internal class JournalTitleChecker : IExtractionMatchValidator
{
    private List<string> _journalTitles = new();
    private readonly IJournalRepository _journalRepository;
    private readonly int _fuzzySharpJournalMatchThreshold;
    private readonly ILogger<JournalTitleChecker> _logger;
    private Task? _loadingTask;

    public JournalTitleChecker(ILogger<JournalTitleChecker> logger, IJournalRepository journalRepository, IConfiguration configuration)
    {
        _fuzzySharpJournalMatchThreshold = configuration.GetValue<int>("DataExtraction:FuzzySharpJournalMatchThreshold");
        _journalRepository = journalRepository;
        _logger = logger;
    }
    public MatchValidationResult GetMatchResult(ExtractedReference extractedReference)
    {
        JournalTitleInitialised();
        var journalTitle = extractedReference.JournalTitle.Value;
        _logger.LogInformation("Extracted Journal Title: {JournalTitle}", journalTitle);

        if (_journalTitles.Count == 0)
        {
            _logger.LogWarning("JournalTitle count is zero");
            return new MatchValidationResult
            {
                Status = MatchingStatus.Failed,
                MatchedValue = null

            };
        }

        var journalTitleUpper = journalTitle.ToUpper();

        var journalMatches = _journalTitles
            .Select(originalJournal =>
            {
                var upperJournal = originalJournal.ToUpper();
                var ratio = Fuzz.Ratio(journalTitleUpper, upperJournal);
                var hasMatch = ratio > _fuzzySharpJournalMatchThreshold;

                return new MatchCheckerModel
                {
                    OriginalValue = originalJournal,
                    Score = ratio,
                    StandardRatio = ratio,
                    InitialismRatio = 0,
                    HasValidMatch = hasMatch
                };
            })
            .Where(match => match.HasValidMatch)
            .OrderByDescending(match => match.Score)
            .ToList();

        var bestMatch = journalMatches.FirstOrDefault();

        if (bestMatch == null)
        {
            _logger.LogWarning("Journal title: {JournalTitle} could not be matched to any journal above threshold.", journalTitle);

            return new MatchValidationResult
            {
                Status = MatchingStatus.Failed,
                MatchedValue = null
            };
        }

        _logger.LogDebug("Best journal match: '{Input}' -> '{Target}' (Score: {Score})",
            journalTitleUpper, bestMatch.OriginalValue?.ToUpper(), bestMatch.Score);

        if (bestMatch.Score == 100)
        {
            _logger.LogInformation("Journal Title: {JournalTitle} perfectly matched to: {MatchedJournal}.",
                journalTitle, bestMatch.OriginalValue);

            return new MatchValidationResult
            {
                Status = MatchingStatus.Passed,
                MatchedValue = bestMatch.OriginalValue
            };
        }
        else
        {
            _logger.LogInformation("Journal Title: {JournalTitle} matched with uncertainty to: {MatchedJournal} (Score: {Score}).",
                journalTitle, bestMatch.OriginalValue, bestMatch.Score);

            return new MatchValidationResult
            {
                Status = MatchingStatus.Unsure,
                MatchedValue = bestMatch.OriginalValue,
            };
        }

    }

    private void JournalTitleInitialised()
    {
        if (_journalTitles.Count > 0)
            return;
        _loadingTask ??= PopulateJournalTitles();
        _loadingTask.GetAwaiter().GetResult();

    }

    private async Task PopulateJournalTitles()
    {
        _journalTitles = (await _journalRepository.GetNames()).ToList();
    }
}