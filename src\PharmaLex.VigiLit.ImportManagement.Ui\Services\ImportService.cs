﻿using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Services;

internal class ImportService : IImportService
{
    private readonly IImportRepository _importRepository;
    private readonly IImportContractRepository _importContractRepository;
    private readonly IAdHocImportContractRepository _adHocImportContractRepository;
    private readonly TimeProvider _timeProvider;

    public ImportService(
        IImportRepository importRepository,
        IImportContractRepository importContractRepository,
        IAdHocImportContractRepository adHocImportContractRepository, 
        TimeProvider timeProvider)
    {
        _importRepository = importRepository;
        _importContractRepository = importContractRepository;
        _adHocImportContractRepository = adHocImportContractRepository;
        _timeProvider = timeProvider;
    }

    public async Task<IEnumerable<ImportModel>> GetImportLog()
    {
        int days = Constants.ImportLogIntervalDays;
        return await _importRepository.GetImportLog(days);
    }

    public async Task<IEnumerable<ImportContractModel>> GetImportContractsLog(int importId)
    {
        return await _importContractRepository.GetImportContractsLog(importId);
    }

    public async Task<IEnumerable<AdHocImportContractModel>> GetAdHocImportContractsList(int adHocImportId)
    {
        return await _adHocImportContractRepository.GetForListDetails(adHocImportId);
    }

    public async Task SaveImport(Guid commandCorrelationId, SourceSystem sourceSystem )
    {
        var importType = sourceSystem switch
        {
            SourceSystem.Web => ImportType.Web,
            SourceSystem.File => ImportType.File,
            SourceSystem.ManualCorrection => ImportType.ManualCorrection,
            _ => ImportType.Manual
        };

        var import = new Import
        {
            ImportType = importType,
            CorrelationId = commandCorrelationId,
            ImportStatusType = ImportStatusType.Queued,
            ImportTriggerType = ImportTriggerType.Manual,
            StartDate = _timeProvider.GetUtcNow().UtcDateTime,
            Message = "Queued"
        };
        _importRepository.Add(import);
        await _importRepository.SaveChangesAsync();
    }
}