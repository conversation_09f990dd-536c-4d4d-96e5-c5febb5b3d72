﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddJournalTitleMatchedAndUpdateJournalTtitleMatchingStatus : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "JournalTitleMatchingCheckPassed",
                table: "FailedImportFiles");

            migrationBuilder.AlterColumn<string>(
                name: "CountryMatched",
                table: "FailedImportFiles",
                type: "nvarchar(128)",
                maxLength: 128,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "JournalTitleMatched",
                table: "FailedImportFiles",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "JournalTitleMatchingStatus",
                table: "FailedImportFiles",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "JournalTitleMatched",
                table: "FailedImportFiles");

            migrationBuilder.DropColumn(
                name: "JournalTitleMatchingStatus",
                table: "FailedImportFiles");

            migrationBuilder.AlterColumn<string>(
                name: "CountryMatched",
                table: "FailedImportFiles",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(128)",
                oldMaxLength: 128,
                oldNullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "JournalTitleMatchingCheckPassed",
                table: "FailedImportFiles",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
