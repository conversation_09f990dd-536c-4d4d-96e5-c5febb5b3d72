﻿using PharmaLex.VigiLit.Scraping.Client.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Interfaces;

public interface IApifyTaskService
{
    Task<string> CreateTaskForJournalAsync(JournalScheduleInfo journal, string taskName, CancellationToken cancellationToken = default);

    Task<string> CreateGroupTaskAsync(IEnumerable<JournalScheduleInfo> journals, string taskName, CancellationToken cancellationToken = default);
}
