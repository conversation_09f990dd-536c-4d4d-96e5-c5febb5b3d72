﻿using Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;

namespace PharmaLex.VigiLit.Search.Ui;
public static class ConfigureServices
{
    public static void RegisterSearchUi(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<ISearchService, SearchService>();
        services.AddScoped<IReferenceClassificationRepository, ReferenceClassificationRepository>();

        var assembly = typeof(ConfigureServices).Assembly;
        services.AddAutoMapper(assembly);

        services.AddControllersWithViews()
            .AddRazorRuntimeCompilation()
            .AddApplicationPart(assembly);

        services.Configure<MvcRazorRuntimeCompilationOptions>(options =>
            { options.FileProviders.Add(new EmbeddedFileProvider(assembly)); });
    }
}