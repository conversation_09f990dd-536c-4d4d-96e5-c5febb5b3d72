using Apify.SDK.Model;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;
using Task = System.Threading.Tasks.Task;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ApifyScheduleService : IApifyScheduleService
{
    private readonly IApifyClient _apifyClient;
    private readonly ILogger<ApifyScheduleService> _logger;

    public ApifyScheduleService(IApifyClient apifyClient, ILogger<ApifyScheduleService> logger)
    {
        _apifyClient = apifyClient;
        _logger = logger;
    }

    public async Task<GetListOfSchedulesResponse> GetSchedulesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _apifyClient.GetSchedulesAsync(cancellationToken);

            _logger.LogInformation("Get Apify schedules");

            return response;
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to get Apify schedules";
            _logger.LogError(ex, LoggingConstants.CreateOrUpdateScheduleErrorTemplate, contextualMessage);
            throw new InvalidOperationException(contextualMessage, ex);
        }
    }

    public async Task CreateScheduleForTaskAsync(string taskId, string scheduleName, string cronExpression, CancellationToken cancellationToken = default)
    {
        try
        {
            await _apifyClient.CreateSchedulesAsync(taskId, scheduleName, cronExpression, "UTC", cancellationToken);

            _logger.LogInformation("Created Apify schedule '{ScheduleName}' for task '{TaskId}'",
                scheduleName, taskId);
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to create Apify schedule '{scheduleName}' for task '{taskId}' with cron expression '{cronExpression}'";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, contextualMessage);
            throw new InvalidOperationException(contextualMessage, ex);
        }
    }

    public async Task UpdateScheduleAsync(string scheduleId, string taskId, CancellationToken cancellationToken = default)
    {
        try
        {
            await _apifyClient.UpdateScheduleAsync(scheduleId, taskId, cancellationToken);

            _logger.LogInformation("Updated Apify schedule '{ScheduleId}' with task '{TaskId}'",
                scheduleId, taskId);
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to update Apify schedule '{scheduleId}' with task '{taskId}'";
            _logger.LogError(ex, LoggingConstants.CreateOrUpdateScheduleErrorTemplate, contextualMessage);
            throw new InvalidOperationException(contextualMessage, ex);
        }
    }
}
