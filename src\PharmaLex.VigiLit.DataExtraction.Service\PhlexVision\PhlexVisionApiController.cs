﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Auditing.Client;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.MessageBroker.Contracts;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;

[Route("api/[controller]")]
[ApiController]
[AllowAnonymous]
public class PhlexVisionApiController : ControllerBase
{

    private readonly ICallbackHandler _callbackHandler;
    private readonly ILogger<PhlexVisionApiController> _logger;
    private readonly IAuditClient _auditClient;
    private readonly IMetaDataExtractor _metaDataExtractor;

    private const string CompletedWithFailures = "Completed with failures";

    public PhlexVisionApiController(
        ICallbackHandler callbackHandler,
        ILogger<PhlexVisionApiController> logger,
        IAuditClient auditClient,
        IMetaDataExtractor metaDataExtractor)
    {
        _callbackHandler = callbackHandler;
        _logger = logger;
        _auditClient = auditClient;
        _metaDataExtractor = metaDataExtractor;
    }

    [HttpGet("DownloadDocument/{documentId}/{filename}/")]
    public async Task<IActionResult> DownloadDocument([FromRoute] DownloadDocumentsCommandRequest downloadRequest,
        string documentId, string filename)
    {
        _logger.LogInformation("PhlexVisionApiController: DownloadDocument: {CorrelationId} : {DocumentId} : {FileName}",
            LogSanitizer.Sanitize(downloadRequest.CorrelationId ?? "Null CorrelationId"),
            LogSanitizer.Sanitize(downloadRequest.DocumentId ?? "Null DocumentId"),
            LogSanitizer.Sanitize(downloadRequest.FileName ?? "Null FileName"));

        if (!TryParse(downloadRequest, out var correlationId))
        {
            await SendStatusChangedEvent(correlationId, CompletedWithFailures);
            throw new ArgumentException($"Correlation Id is empty for {downloadRequest.FileName}");
        }

        return new FileStreamResult(await _callbackHandler.GetDocumentStream(correlationId), "application/pdf")
        {
            FileDownloadName = filename,
        };
    }

    [HttpPost("success/{documentId}/")]
    [IgnoreAntiforgeryToken]
    public async Task<IActionResult> Success([FromRoute][FromBody] SuccessCallbackRequest request, string documentId)
    {
        _logger.LogInformation("PhlexVisionApiController: Success: {CorrelationId}",
            LogSanitizer.Sanitize(request.CorrelationId ?? "Null CorrelationId"));

        if (!TryParse(request, out var correlationId))
        {
            await BadRequestWithStatusUpdate(correlationId);
        }

        var (contextInfo, extractedMetaData) = await _metaDataExtractor.GetExtractedMetaData(HttpContext.Request.Body, correlationId);
        await _callbackHandler.Success(correlationId, extractedMetaData!, contextInfo);

        return Ok();
    }

    [HttpPost("error/{documentId}/")]
    [IgnoreAntiforgeryToken]
    public async Task<IActionResult> Error([FromRoute][FromBody] ErrorCallbackRequest request, string documentId)
    {
        _logger.LogInformation("PhlexVisionApiController: Error: {CorrelationId}",
            LogSanitizer.Sanitize(request.CorrelationId ?? "Null CorrelationId"));

        if (!TryParse(request, out var correlationId))
        {
            await BadRequestWithStatusUpdate(correlationId);
        }

        await SendStatusChangedEvent(correlationId, CompletedWithFailures);
        await _callbackHandler.Error(correlationId);

        return Ok();
    }

    private async Task<BadRequestResult> BadRequestWithStatusUpdate(Guid correlationId)
    {
        await SendStatusChangedEvent(correlationId, CompletedWithFailures);
        return base.BadRequest();
    }

    private static bool TryParse(IHasCorrelationId request, out Guid correlationId)
    {
        return Guid.TryParse(request.CorrelationId, out correlationId);
    }

    private async Task SendStatusChangedEvent(Guid correlationId, string statusMessage)
    {
        var statusChangedEvent = new StatusChangedEvent(nameof(ImportType.File), correlationId,
            DateTime.UtcNow, "<EMAIL>", statusMessage);
        await _auditClient.Send(statusChangedEvent);
    }
}
