﻿using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Options;
using Moq;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Import;

namespace PharmaLex.VigiLit.Scraping.Service.Tests
{
    public class DownloadBlobStorageTests
    {
        private readonly Mock<IDocumentService> _mockDocumentService;
        private readonly Mock<IBlobContainerClientProvider> _mockBlobContainerClientProvider;
        private readonly Mock<IOptions<AzureStorageImportScrapeDocumentOptions>> _mockOptions;
        private readonly AzureStorageImportScrapeDocumentOptions _options;
        private readonly DownloadBlobStorage _downloadBlobStorage;

        public DownloadBlobStorageTests()
        {
            _mockDocumentService = new Mock<IDocumentService>();
            _mockBlobContainerClientProvider = new Mock<IBlobContainerClientProvider>();
            _mockOptions = new Mock<IOptions<AzureStorageImportScrapeDocumentOptions>>();

            _options = new AzureStorageImportScrapeDocumentOptions
            {
                AccountName = "testaccount",
                ContainerName = "testcontainer"
            };

            _mockOptions.Setup(x => x.Value).Returns(_options);

            _downloadBlobStorage = new DownloadBlobStorage(
                _mockDocumentService.Object,
                _mockOptions.Object,
                _mockBlobContainerClientProvider.Object);
        }

        [Fact]
        public void SetBlobFolderName_SetsFolderName()
        {
            // Arrange
            var folderName = "testfolder";

            // Act
            _downloadBlobStorage.SetBlobFolderName(folderName);

            // Assert
            Assert.Equal(folderName, GetPrivateFieldValue<string>(_downloadBlobStorage, "_folderName"));
        }

        [Fact]
        public async Task WriteDataItemAsync_CallsDocumentServiceWithCorrectDescriptor()
        {
            // Arrange
            var fileName = "testfile.pdf";
            var folderName = "testfolder";
            var stream = new MemoryStream();

            _downloadBlobStorage.SetBlobFolderName(folderName);

            var mockBlobClient = new Mock<BlobClient>();
            DocumentDescriptor? capturedDescriptor = null;

            _mockDocumentService.Setup(x => x.Create(
                It.IsAny<DocumentDescriptor>(),
                It.IsAny<Stream>(),
                It.IsAny<CancellationToken>()))
                .Callback<DocumentDescriptor, Stream, CancellationToken>(
                    (d, s, ct) => capturedDescriptor = d)
                .ReturnsAsync(mockBlobClient.Object); 

            // Act
            await _downloadBlobStorage.WriteDataItemAsync(fileName, stream);

            // Assert
            Assert.NotNull(capturedDescriptor);
            Assert.Equal(_options.AccountName, capturedDescriptor.AccountName);
            Assert.Equal(_options.ContainerName, capturedDescriptor.ContainerName);
            Assert.Equal($"scraping/{folderName}/{fileName}", capturedDescriptor.BlobName);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public async Task GetBlobPathsAsync_ThrowsArgumentNullException_WhenFolderNameIsNullOrEmpty(string folderName)
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => _downloadBlobStorage.GetBlobPathsAsync(folderName));
        }

        [Fact]
        public async Task GetBlobPathsAsync_ReturnsBlobPaths_WithCorrectPrefix()
        {
            // Arrange
            var folderName = "testfolder";
            var prefix = $"scraping/{folderName}/";
            var expectedBlobPaths = new List<string>
            {
                $"scraping/{folderName}/file1.pdf",
                $"scraping/{folderName}/file2.pdf"
            };

            var mockContainerClient = new Mock<BlobContainerClient>();
            _mockBlobContainerClientProvider.Setup(x => x.Provide(It.IsAny<DocumentDescriptor>()))
                .Returns(mockContainerClient.Object);

            // Create a mock AsyncPageable<BlobItem>
            var pageable = CreateMockAsyncPageable(expectedBlobPaths);
            mockContainerClient.Setup(x => x.GetBlobsAsync(
                    It.Is<BlobTraits>(t => t == BlobTraits.None),
                    It.Is<BlobStates>(s => s == BlobStates.None),
                    It.Is<string>(p => p == prefix),
                    It.IsAny<CancellationToken>()))
                .Returns(pageable);

            // Act
            var result = await _downloadBlobStorage.GetBlobPathsAsync(folderName);

            // Assert
            Assert.Equal(expectedBlobPaths, result);
        }

        // Helper method to access private field via reflection
        private static T GetPrivateFieldValue<T>(object instance, string fieldName)
        {
            var field = instance.GetType().GetField(fieldName,
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return (T)field!.GetValue(instance)!;
        }

        // Helper method to create a proper AsyncPageable<BlobItem> that the Azure SDK expects
        private static AsyncPageable<BlobItem> CreateMockAsyncPageable(IEnumerable<string> blobPaths)
        {
            var pages = new List<Page<BlobItem>>();
            var items = new List<BlobItem>();

            foreach (var path in blobPaths)
            {
                items.Add(BlobsModelFactory.BlobItem(
                    name: path,
                    false,
                    properties: BlobsModelFactory.BlobItemProperties(
                        false,
                        contentLength: 1024,
                        lastModified: DateTimeOffset.Now)));
            }

            pages.Add(Page<BlobItem>.FromValues([.. items], null, Mock.Of<Response>()));

            return AsyncPageable<BlobItem>.FromPages(pages);
        }
    }
}