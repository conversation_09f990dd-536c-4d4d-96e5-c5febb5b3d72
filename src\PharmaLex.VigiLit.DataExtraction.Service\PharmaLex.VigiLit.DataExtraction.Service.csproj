﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests"></InternalsVisibleTo>
	</ItemGroup>
	
	<ItemGroup>
	  <ProjectReference Include="..\PharmaLex.Core.Configuration\PharmaLex.Core.Configuration.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.Auditing.Client\PharmaLex.VigiLit.Auditing.Client.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.DataExtraction.Client\PharmaLex.VigiLit.DataExtraction.Client.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.DataExtraction.Entities\PharmaLex.VigiLit.DataExtraction.Entities.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Client\PharmaLex.VigiLit.ImportManagement.Client.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.Logging\PharmaLex.VigiLit.Logging.csproj" />
	</ItemGroup>
	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests"></InternalsVisibleTo>
		<InternalsVisibleTo Include="DynamicProxyGenAssembly2"></InternalsVisibleTo>
		<InternalsVisibleTo Include="PhlexVisionTestHarness"></InternalsVisibleTo>
	</ItemGroup>
	<ItemGroup>
	  <PackageReference Include="AutoMapper" Version="14.0.0" />
	  <PackageReference Include="FuzzySharp" Version="2.0.2" />
	  <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
	  <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7" />
	  <PackageReference Include="PharmaLex.BlobStorage" Version="8.0.0.126" />
	</ItemGroup>
</Project>
