﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;
using Task = System.Threading.Tasks.Task;

namespace PharmaLex.VigiLit.Scraping.Service;

public class ApifyNotification : IApifyNotification
{
    private readonly IApifyClient apifyClient;
    private readonly IDataExtractionClient client;
    private readonly IDownloadBlobStorage downloadStorage;
    private readonly IApifyFileGroupingService apifyFileGroupingService;
    private readonly ILogger<ApifyNotification> logger;

    public ApifyNotification(
        IApifyClient apifyClient,
        IDownloadBlobStorage downloadStorage,
        IDataExtractionClient client,
        IApifyFileGroupingService apifyFileGroupingService,
        ILogger<ApifyNotification> logger)
    {
        this.apifyClient = apifyClient;
        this.downloadStorage = downloadStorage;
        this.client = client;
        this.apifyFileGroupingService = apifyFileGroupingService;
        this.logger = logger;
    }

    public async Task RunSucceeded(ApifyWebhookPayload runData)
    {
        if (runData == null || runData.resource == null || runData.eventData == null ||
            string.IsNullOrEmpty(runData.resource.defaultKeyValueStoreId) ||
            string.IsNullOrEmpty(runData.resource.defaultDatasetId) ||
            string.IsNullOrEmpty(runData.eventData.actorRunId))
        {
            logger.LogError("ApifyNotification: Insufficient run data {RunData}", runData);
            return;
        }

        try
        {
            logger.LogInformation("ApifyNotification: TransferFilesAsync started for runId: {RunId},DataSetId: {DataSetId},KeyValueStoreId: {KeyValueStoreId}", runData.eventData.actorRunId, runData.resource.defaultDatasetId, runData.resource.defaultKeyValueStoreId);
            var runId = runData.eventData.actorRunId ?? string.Empty;

            downloadStorage.SetBlobFolderName(runId);

            await apifyClient.TransferFilesAsync(runData.resource.defaultDatasetId, runData.resource.defaultKeyValueStoreId, downloadStorage);

            logger.LogInformation("ApifyNotification: TransferFilesAsync ended for runId: {RunId},DataSetId: {DataSetId}, KeyValueStoreId: {KeyValueStoreId}", runData.eventData.actorRunId, runData.resource.defaultDatasetId, runData.resource.defaultKeyValueStoreId);

            await CreateAndSendExtractDataCommand(runId);

            if (runData.eventData != null && runData.eventData.actorRunId != null)
            {
                await apifyClient.CleanupActorRunAsync(runData.eventData.actorRunId);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "ApifyNotification: An error occured: {ErrorMessage}", ex.Message);
        }
    }

    public async Task RunFailed(ApifyWebhookPayload runData)
    {
        if (runData is null || runData.resource is null)
        {
            logger.LogError("ApifyNotification: RunFailed called with insufficient run data {RunData}", runData);
            return;
        }

        try
        {
            var taskId = runData.resource.actorTaskId ?? "";
            var datasetId = runData.resource.defaultDatasetId ?? "";
            var keyValueStoreId = runData.resource.defaultKeyValueStoreId ?? "";
            var createdAt = runData.createdAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "";

            logger.LogError("ApifyNotification: Scraping run failure - TaskId: {TaskId}, DataSetId: {DataSetId}, KeyValueStoreId: {KeyValueStoreId}, Timestamp: {CreatedAt}",
                taskId, datasetId, keyValueStoreId, createdAt);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "ApifyNotification: An error occurred while processing run failure: {ErrorMessage}", ex.Message);
        }

        await Task.CompletedTask;
    }

    private async Task CreateAndSendExtractDataCommand(string runId)
    {
        Dictionary<string, List<string>> filesByJournal;

        try
        {
            filesByJournal = await apifyFileGroupingService.GroupFilesByJournalUsingKeyValueStoreAsync(runId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "ApifyNotification: Failed to get file associations from key-value store for run {runId}", runId);

            var blobPaths = await downloadStorage.GetBlobPathsAsync(runId);
            filesByJournal = new Dictionary<string, List<string>>
            {
                ["Default"] = blobPaths.ToList()
            };
        }

        foreach (var journalGroup in filesByJournal)
        {
            var batchId = Guid.NewGuid();

            logger.LogInformation("ApifyNotification: Creating batch {BatchId} for journal group {JournalGroup} with {FileCount} files",
                batchId, journalGroup.Key, journalGroup.Value.Count);

            foreach (var filePath in journalGroup.Value)
            {
                var command = new ExtractDataCommand()
                {
                    BatchId = batchId.ToString(),
                    FileName = filePath,
                    Source = Source.File,
                    CorrelationId = Guid.NewGuid()
                };
                await client.Send(command);
            }
        }
    }
}