﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="TestData\Abecasis R, 2024 1.pdf" />
    <None Remove="TestData\de Almeida MT, 2024 1.pdf" />
    <None Remove="TestData\Filipe RC, 2024 1.pdf" />
    <None Remove="TestData\Galvao J, 2024 1.pdf" />
    <None Remove="TestData\Haloi P, 2024 1.pdf" />
    <None Remove="TestData\Novais C, 2024 1.pdf" />
    <None Remove="TestData\Ribau R, 2024 1.pdf" />
    <None Remove="TestData\Tavares T, 2024 2.pdf" />
    <None Remove="TestData\TESTDATA1.pdf" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="TestData\Abecasis R, 2024 1.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="TestData\TESTDATA2.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="TestData\de Almeida MT, 2024 1.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="TestData\Filipe RC, 2024 1.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="TestData\Galvao J, 2024 1.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="TestData\Haloi P, 2024 1.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="TestData\Novais C, 2024 1.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="TestData\Ribau R, 2024 1.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="TestData\Tavares T, 2024 2.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="TestData\TESTDATA1.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.TimeProvider.Testing" Version="9.7.0" />
    <PackageReference Include="PharmaLex.BlobStorage" Version="8.0.0.126" />
    <PackageReference Include="TextCopy" Version="6.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.DataExtraction.Service\PharmaLex.VigiLit.DataExtraction.Service.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.ImportManagement.Service\PharmaLex.VigiLit.ImportManagement.Service.csproj" />
  </ItemGroup>

	<PropertyGroup>
		<GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
		<UserSecretsId>PharmaLex.VigiLit</UserSecretsId>
	</PropertyGroup>

</Project>
