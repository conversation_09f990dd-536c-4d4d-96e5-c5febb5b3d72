{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "System": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Warning"}, "ApplicationInsights": {"LogLevel": {"Default": "Warning", "VigiLit": "Information"}}}, "AllowedHosts": "*", "ConnectionStrings": {"default": "Server=(local); Database=vigilit-dev; Trusted_connection=true; TrustServerCertificate=true;"}, "AzureAdB2C": {"Instance": "https://smartphlexb2c.b2clogin.com/", "ClientId": "e3160fd4-4df9-4eb3-9251-9d713d19f31f", "Domain": "smartphlexb2c.onmicrosoft.com", "SignedOutCallbackPath": "/signout/B2C_1_signin", "SignUpSignInPolicyId": "B2C_1_signup_signin_plx", "ResetPasswordPolicyId": "b2c_1_password_reset", "EditProfilePolicyId": "b2c_1_profile_edit", "CallbackPath": "/signin-oidc"}, "AzureAdB2CPolicy": {"Tenant": "smartphlexb2c", "TenantId": "f8b90da3-e024-492c-800e-1b793397f942", "ClientId": "e3160fd4-4df9-4eb3-9251-9d713d19f31f", "SigningCertThumbprint": "E1BA3541BA2553B9BC40927F1359F264E1724D61", "LinkExpiresAfterDays": 30, "Policies": [{"PolicyId": "B2C_1A_signup_invitation", "CallbackPath": "/signin-oidc-invite", "ApplicationCallbackPath": "/signup-invitation"}, {"PolicyId": "B2C_1_signup_signin_pharmalex", "CallbackPath": "/signin-oidc-plx", "ApplicationCallbackPath": "/signin"}, {"PolicyId": "B2C_1_signin_local", "CallbackPath": "/signin-local"}, {"PolicyId": "B2C_1_signin_federated", "CallbackPath": "/signin-federated"}, {"PolicyId": "B2C_1A_signin_signature", "CallbackPath": "/signin-signature-oidc", "ApplicationCallbackPath": "/signature"}], "Domains": [["pharmalex.com", "yespharmaservices.onmicrosoft.com"]]}, "AzureAdGraph": {"ClientId": "3c6a0ac3-0014-4890-974e-699bdbbcba87", "Domain": "yes-services.eu"}, "KeyVaultName": "vgt-dev-kv-eun", "AzureStorage": {"CaseDocumentUpload": {"AccountName": "vgtdevpubliceun", "ContainerName": "case-document-upload"}, "CaseDocument": {"AccountName": "vgtdevsharedeun", "ContainerName": "case-document"}, "TrackingSheetDocument": {"AccountName": "vgtdevsharedeun", "ContainerName": "tracking-sheet-document"}, "ImportFileDocumentUpload": {"AccountName": "vgtdevsharedeun", "ContainerName": "import-file-document-upload"}, "ImportFileDocument": {"AccountName": "vgtdevsharedeun", "ContainerName": "import-file-document"}, "ImportScrapeDocument": {"AccountName": "vgtdevsharedeun", "ContainerName": "scrape-document"}}, "VisualStudioTenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77", "AzureAdB2CGraph": {"ClientId": "5c76d17d-764a-4e8e-bef7-c39afd6fb3bd", "Domain": "smartphlexb2c.onmicrosoft.com"}, "Migrations": {"Assembly": "VigiLit.Infrastructure.Data"}, "Static": {"App": "content", "Env": "v1.38.0", "Cdn": "https://phcgvcdn-endpoint.azureedge.net"}, "EmailSettings": {"NoReplyEmail": "<EMAIL>", "NoReplyEmailName": "VigiLit 7", "InfoEmail": "<EMAIL>", "OrderingEmail": "<EMAIL>", "DailyReferenceClassificationEmailTemplateId": "d-cbb1271e7c624dd299a3032f44bf73f9", "InvitationEmailTemplateId": "d-f73d79c558e74f86a354de3deee629b9", "CaseEmailTemplateId": "d-90ecabd7bad948a9b1cb1e20e0904f92", "CaseEmailAttachmentMaxBytes": ********}, "UserSession": {"InactivityLimitSeconds": 3600, "SessionPollingTimeMilliseconds": 60000}, "AppSettings": {"SmartPHLEXAdminEmail": "<EMAIL>", "BuildInfo": "Build:local", "BuildNumber": "local", "Version": "7.17.0", "EnvironmentName": "dev", "AppName": "VigiLit"}, "MessageBus": {"TransportType": "AzureServiceBus", "RabbitMq": {"Host": "localhost", "Port": 5672, "VirtualHost": "/"}, "AzureServiceBus": {"Namespace": "vgt-dev-servicebus-eun", "ConnectionString": "vault"}}, "PhlexVision": {"url": "https://phlexvision-staging.phlexglobal.com/api/ocrapi/v1/documents", "ConsumerKeyHeaderValue": "mock", "OpenAiConfigId": "75AEFBDC-0F2A-4D94-85F2-E0D2C7E730AE", "Secret": "", "CallbackBaseUrl": ""}, "DataExtraction": {"MandatoryFieldMinimumConfidenceLevel": "0.85", "NonMandatoryFieldMinimumConfidenceLevel": "0.0", "OverallMinimumConfidenceLevel": "0.0", "MandatoryWeight": "2.0", "NonMandatoryWeight": "0.0", "FuzzySharpJournalMatchThreshold": "90", "FuzzySharpCountryMatchThreshold": "90", "FuzzySharpCountryInitialismThreshold": "80"}, "Apify": {"BaseUri": "https://api.apify.com", "AccessToken": "**********************************************", "SecretToken": "GZY8wVnNsGlU2NMryuSA"}, "HostUri": "https://vigilit-dev.smartphlex.com/"}