. '..\..\Helpers\git\get-gitconfig.ps1'

Write-Host Seed database -Foreground Green 

$user = Get-GitConfig -scope global | where Category -eq 'user'
$userName = ($user | where name -eq 'name').Value
$userEmail = ($user | where name -eq 'email').Value
Write-Host $userName
Write-Host $userEmail

$SQLServer = "localhost"
$db = "VigiLitDb"
$sa = "sa"
$password = "Password1."
$selectdata = "SELECT Id, IdData FROM invokeTable"

Invoke-SqlCmd -ServerInstance $SQLServer -Database $db -Username "sa" -Password "Password1." -Query $selectdata

Write-Host Completed. -Foreground Green 
