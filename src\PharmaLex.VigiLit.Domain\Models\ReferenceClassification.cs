﻿using PharmaLex.VigiLit.AiAnalysis.Entities;
using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ReferenceManagement;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.VigiLit.Domain.Models;

public class ReferenceClassification : VigiLitEntityBase
{
    public int ReferenceId { get; set; }
    public Reference Reference { get; set; }
    public int SubstanceId { get; set; }
    public Substance Substance { get; set; }
    public int? ClassificationCategoryId { get; set; }
    public ClassificationCategory ClassificationCategory { get; set; }
    public ReferenceState ReferenceState { get; set; }
    public string DosageForm { get; set; }
    public string MinimalCriteria { get; set; }
    public PSURRelevanceAbstract PSURRelevanceAbstract { get; set; } = PSURRelevanceAbstract.NA;
    public string CountryOfOccurrence { get; set; }
    public int? ClassifierId { get; set; }
    public User Classifier { get; set; }
    public string ReasonForChange { get; set; }
    public bool IsActive { get; set; }
    public int? PreAssessorId { get; set; }
    public int? MasterAssessorId { get; set; }

    public string AiSuggestedCategory { get; set; }
    public string AiCategoryReason { get; set; }
    public AiCategoryDecision AiCategoryDecision { get; set; }
    public string AiSuggestedDosageForm { get; set; }
    public string AiDosageFormReason { get; set; }
    public AiDosageFormDecision AiDosageFormDecision { get; set; }

    [MaxLength(100)]
    public string PvSafetyDatabaseId { get; set; }
    public string SpecialSituations { get; set; }

    public ICollection<ReferenceHistoryAction> ReferenceHistoryActions { get; set; } = new List<ReferenceHistoryAction>();

    public ICollection<ImportContractReferenceClassification> ImportContractReferenceClassifications { get; set; } = new List<ImportContractReferenceClassification>();

    public ReferenceClassification() { }

    public ReferenceClassification(Reference reference, int substanceId)
    {
        ReferenceState = ReferenceState.New;
        IsActive = true;

        Reference = reference;
        SubstanceId = substanceId;
    }

    public ReferenceClassification(int referenceId, int substanceId)
    {
        ReferenceState = ReferenceState.New;
        IsActive = true;

        ReferenceId = referenceId;
        SubstanceId = substanceId;
    }

    public void Deactivate()
    {
        ReferenceState = ReferenceState.Inactive;
        IsActive = false;
    }

    public void PreClassify(int classificationCategoryId, string dosageForm, string countryOfOccurrence, int preclassifiedByUserId)
    {
        ReferenceState = ReferenceState.Preclassified;
        IsActive = true;

        ClassificationCategoryId = classificationCategoryId;
        DosageForm = dosageForm;
        CountryOfOccurrence = countryOfOccurrence;
        ClassifierId = preclassifiedByUserId;
        PreAssessorId = preclassifiedByUserId;
    }

    public void SetAiCategoryFields(AiCategoryDecision aiCategoryDecision, string aiSuggestedCategory, string aiCategoryReason)
    {
        AiCategoryDecision = aiCategoryDecision;
        AiSuggestedCategory = aiSuggestedCategory;
        AiCategoryReason = aiCategoryReason;
    }

    public void SetAiDosageFormFields(AiDosageFormDecision aiDosageFormDecision, string aiSuggestedDosageForm, string aiDosageFormReason)
    {
        AiDosageFormDecision = aiDosageFormDecision;
        AiSuggestedDosageForm = aiSuggestedDosageForm;
        AiDosageFormReason = aiDosageFormReason;
    }

    public void UpdateDosageForm(string dosageForm, int preclassifiedByUserId)
    {
        IsActive = true;
        DosageForm = dosageForm;
        ClassifierId = preclassifiedByUserId;
        PreAssessorId = preclassifiedByUserId;
    }

    public void RePreclassify(int classificationCategoryId, string dosageForm, string countryOfOccurrence)
    {
        ClassificationCategoryId = classificationCategoryId;
        DosageForm = dosageForm;
        CountryOfOccurrence = countryOfOccurrence;
    }

    public void Classify(int classificationCategoryId, string specialSituations, string dosageForm, string countryOfOccurrence, int classifyByUserId, string minimalCriteria)
    {
        ReferenceState = ReferenceState.Approved;

        ClassificationCategoryId = classificationCategoryId;
        SpecialSituations = specialSituations;
        DosageForm = dosageForm;
        CountryOfOccurrence = countryOfOccurrence;
        MinimalCriteria = minimalCriteria;
        ClassifierId = classifyByUserId;
        MasterAssessorId = classifyByUserId;
    }

    public void ReClassify(int classificationCategoryId, string specialSituations, string dosageForm, string countryOfOccurrence, int classifiedByUserId, string minimalCriteria, string reasonForChange)
    {
        IsActive = true;

        ClassificationCategoryId = classificationCategoryId;
        SpecialSituations = specialSituations;
        DosageForm = dosageForm;
        CountryOfOccurrence = countryOfOccurrence;
        ClassifierId = classifiedByUserId;
        MasterAssessorId = classifiedByUserId;
        MinimalCriteria = minimalCriteria;
        ReasonForChange = reasonForChange;
    }

    public void UpdatePSUR(string psurRelevanceAbstract)
    {
        PSURRelevanceAbstract = Enum.Parse<PSURRelevanceAbstract>(psurRelevanceAbstract);
    }

    public void Sign()
    {
        ReferenceState = ReferenceState.Signed;
    }

    public ReferenceClassification Copy()
    {
        return new ReferenceClassification()
        {
            ReferenceId = ReferenceId,
            SubstanceId = SubstanceId,
            ClassificationCategoryId = ClassificationCategoryId,
            DosageForm = DosageForm,
            CountryOfOccurrence = CountryOfOccurrence,
            PSURRelevanceAbstract = PSURRelevanceAbstract,
            PvSafetyDatabaseId = PvSafetyDatabaseId,
            ClassifierId = ClassifierId,
            MasterAssessorId = MasterAssessorId,
            MinimalCriteria = MinimalCriteria,
            ReasonForChange = ReasonForChange,
            SpecialSituations = SpecialSituations
        };

    }
}