﻿namespace PharmaLex.VigiLit.DataExtraction.Service.Data;

#pragma warning  disable CS8618
internal class RootObjectMultiPrompt
{
    public string Text { get; set; }
    public string TranslatedText { get; set; }
    public string MarkdownText { get; set; }
    public bool IsOcr { get; set; }
    public string Client { get; set; }
    public Languages Languages { get; set; }
    public AiProcessing AiProcessing { get; set; }
    public StageStatus[] StageStatuses { get; set; } // These don't seem to appear yet so need investigating
}
