﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using PharmaLex.VigiLit.Auditing.Client;
using PharmaLex.VigiLit.DataExtraction.Service.CrossCutting;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Mime;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using static PharmaLex.VigiLit.DataExtraction.Service.PhlexVision.PhlexVisionConstants;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
internal class PhlexVisionService : IPhlexVisionService
{
    private readonly ILogger<PhlexVisionService> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly IAuditClient _auditClient;
    private readonly IDocumentProcessModelFactory _documentProcessModelFactory;
    private readonly ICrossCuttingContext<PhlexVisionService> _ctx;

    private const string Extracting = "Extracting";
    private const string CompletedWithFailures = "Completed with failures";

    public PhlexVisionService(ILogger<PhlexVisionService> logger, IHttpClientFactory httpClientFactory, IConfiguration configuration, IAuditClient auditClient, IDocumentProcessModelFactory documentProcessModelFactory, ICrossCuttingContext<PhlexVisionService> ctx)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _auditClient = auditClient;
        _documentProcessModelFactory = documentProcessModelFactory;
        _ctx = ctx;
    }

    public async Task RequestDataExtraction(ExtractRequest extractRequest)
    {
        var jwtToken = GetJwtToken();

        _ctx.LogInformation("Retrieved JWT token");

        using HttpRequestMessage request = new(HttpMethod.Post, _configuration[PhlexVisionUrl]);
        using HttpClient client = _httpClientFactory.CreateClient();

        var json = _documentProcessModelFactory.GetDocumentProcessModel(jwtToken, extractRequest);
        HttpContent content = new StringContent(
            json,
            Encoding.UTF8,
            MediaTypeNames.Application.Json);

        request.Content = content;
        var consumerKey = _configuration[ConsumerKeyHeaderValue];
        request.Headers.Add(ConsumerKeyHeader, consumerKey);
        request.Headers.Add(AuthorizationHeader, $"Bearer {jwtToken}");
        request.Headers.Add(CorrelationIdHeader, extractRequest.CorrelationId.ToString());

        var response = await client.SendAsync(request);

        var memoryStream = new MemoryStream(100000);
        await JsonSerializer.SerializeAsync(memoryStream, response.Headers);

        using var reader = new StreamReader(memoryStream);
        var body = await reader.ReadToEndAsync();
        _logger.LogInformation("{Body}", LogSanitizer.Sanitize(body));

        await HandleDocumentProcessRequestResponse(response);

        _logger.LogInformation("PhlexVisionService: API Response: {StatusCode} - {Response}", LogSanitizer.Sanitize(response.StatusCode.ToString()), LogSanitizer.Sanitize(response.ToString()));
    }

#pragma warning disable S6781 // key is coming from Secrets or KV
    private string GetJwtToken()
    {
        var keySecretValue = _configuration[SecretValue] ?? throw new ArgumentException("keySecretValue not set");

        SymmetricSecurityKey key = new(Encoding.UTF8.GetBytes(keySecretValue));
        SigningCredentials signingCredentials = new(key, SecurityAlgorithms.HmacSha256, SecurityAlgorithms.Sha256Digest);

        JwtSecurityToken token = new(
            claims: new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, "OCR"),
            },
            expires: DateTime.UtcNow.AddHours(24),
            notBefore: DateTime.UtcNow.AddMinutes(-5),
            signingCredentials: signingCredentials);

        var tokenVal = new JwtSecurityTokenHandler().WriteToken(token);
        return tokenVal;
    }
#pragma warning restore S6781

    private async Task HandleDocumentProcessRequestResponse(HttpResponseMessage response)
    {
        if (!response.IsSuccessStatusCode)
        {
            var correlationId = new Guid(response.Headers.NonValidated[CorrelationIdHeader].First());
            await SendStatusChangedEvent(correlationId, CompletedWithFailures);
            _logger.LogInformation("PhlexVisionService: Failed");
        }
        else
        {
            var correlationId = new Guid(response.Headers.NonValidated[CorrelationIdHeader].First());
            await SendStatusChangedEvent(correlationId, Extracting);
            _logger.LogInformation("PhlexVisionService: CorrelationId : {CorrelationId}", correlationId);
            _logger.LogInformation("PhlexVisionService: Successfully submitted");
        }
    }

    private async Task SendStatusChangedEvent(Guid correlationId, string statusMessage)
    {
        var statusChangedEvent = new StatusChangedEvent(nameof(ImportType.File), correlationId,
            DateTime.UtcNow, "<EMAIL>", statusMessage);
        await _auditClient.Send(statusChangedEvent);
    }
}
