﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using PharmaLex.VigiLit.Test.Framework.Fakes;
using Xunit;

namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests.PhlexVision;
public class CompositeQualityCheckerTests
{
    private readonly Dictionary<string, string?> _inMemorySettings = new Dictionary<string, string?>
    {
        { "DataExtraction:MandatoryFieldMinimumConfidenceLevel", "0.85" },
        { "DataExtraction:NonMandatoryFieldMinimumConfidenceLevel", "0.85" },
        { "DataExtraction:OverallMinimumConfidenceLevel", "0.85" },
        { "DataExtraction:MandatoryWeight", "2.0" },
        { "DataExtraction:NonMandatoryWeight", "1.0" },
        { "DataExtraction:FuzzySharpCountryMatchThreshold", "90" },
        { "DataExtraction:FuzzySharpCountryInitialismThreshold", "60" }
    };

    private readonly Mock<ILogger<CompositeQualityChecker>> _mockLogger = new();
    private readonly Mock<ILogger<MandatoryFieldsValueChecker>> _mockLoggerFieldsValueChecker = new();
    private readonly Mock<ILogger<MandatoryFieldsConfidenceChecker>> _mockLoggerMandatoryFieldsConfidenceChecker = new();
    private readonly Mock<ILogger<NonMandatoryFieldsConfidenceChecker>> _mockLoggerNonMandatoryFieldsConfidenceChecker = new();
    private readonly Mock<ILogger<OverallWeightedConfidenceChecker>> _mockLoggerWeightedConfidenceChecker = new();
    private readonly Mock<ILogger<JournalTitleChecker>> _mockLoggerJournalTitleChecker = new();
    private readonly Mock<IJournalRepository> _journalRepository = new();
    private readonly Mock<ILogger<CountryOfOccurrenceChecker>> _mockLoggerCountryOfOccurrenceChecker = new();
    private readonly Mock<ICountryRepository> _countryRepository = new();

    private readonly IConfiguration _configuration;

    public CompositeQualityCheckerTests()
    {
        var fakeFactory = new FakeLoggerFactory<CompositeQualityChecker>(_mockLogger);

        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(_inMemorySettings)
            .Build();

    }

    [Fact]
    public void Given_extracted_reference_When_all_fields_set_and_above_thresholds_Then_passes_QC()
    {
        // Arrange 
        var extractedReference = GetBlankExtractedReference();
        var composite = CreateValidator();

        // Act
        var result = composite.IsValid(extractedReference);


        // Assert
        Assert.True(result);
    }

    [Theory]
    [InlineData("Title")]
    [InlineData("Abstract")]
    [InlineData("JournalTitle")]
    [InlineData("CountryOfOccurrence")]
    public void Given_extracted_reference_When_mandatory_property_is_empty_Then_fails_QC(string propertyName)
    {
        // Arrange 
        var extractedReference = GetExtractedReferenceWithStringEmptyProperty(propertyName);
        var composite = CreateValidator();

        // Act
        var result = composite.IsValid(extractedReference);

        // Assert
        Assert.False(result);
    }

    [Theory]
    [InlineData("Title")]
    [InlineData("Abstract")]
    [InlineData("JournalTitle")]
    [InlineData("CountryOfOccurrence")]
    public void Given_extracted_reference_When_mandatory_property_confidence_is_below_threshold_Then_fails_QC(string propertyName)
    {
        // Arrange 
        var extractedReference = GetExtractedReferenceWithConfidenceSet(propertyName, 0.50F);
        var composite = CreateValidator();

        // Act
        var result = composite.IsValid(extractedReference);

        // Assert
        Assert.False(result);
    }

    [Theory]
    [InlineData("Doi")]
    [InlineData("IssueNumber")]
    [InlineData("Volume")]
    [InlineData("Issn")]
    [InlineData("Year")]
    [InlineData("Pages")]
    [InlineData("CountryOfOccurrence")]
    [InlineData("Keywords")]
    public void Given_extracted_reference_When_non_mandatory_property_confidence_is_below_threshold_Then_fails_QC(string propertyName)
    {
        // Arrange 
        var extractedReference = GetExtractedReferenceWithConfidenceSet(propertyName, 0.50F);
        var composite = CreateValidator();

        // Act
        var result = composite.IsValid(extractedReference);

        // Assert
        Assert.False(result);
    }

    private static ExtractedReference GetExtractedReferenceWithStringEmptyProperty(string propertyName)
    {
        var extractedReference = GetBlankExtractedReference();

        var propertyInfo = typeof(ExtractedReference).GetProperty(propertyName);
        var extractedProperty = propertyInfo?.GetValue(extractedReference) as IExtractedProperty;
        var valueProp = extractedProperty?.GetType().GetProperty("Value");
        valueProp?.SetValue(extractedProperty, string.Empty);

        return extractedReference;
    }

    private static ExtractedReference GetExtractedReferenceWithConfidenceSet(string propertyName, float confidence)
    {
        var extractedReference = GetBlankExtractedReference();

        var propertyInfo = typeof(ExtractedReference).GetProperty(propertyName);
        var extractedProperty = propertyInfo?.GetValue(extractedReference) as IExtractedProperty;
        var valueProp = extractedProperty?.GetType().GetProperty("Confidence");
        valueProp?.SetValue(extractedProperty, confidence);

        return extractedReference;
    }

    private static ExtractedReference GetBlankExtractedReference()
    {
        var extractedReference = new ExtractedReference
        {
            Title = new Title()
            {
                Confidence = 95.0F,
                Value = "--TITLE--"
            },
            Abstract = new Abstract()
            {
                Confidence = 95.0F,
                Value = "--ABSTRACT--",
            },
            Authors = new Author[]
            {
                new Author()
                {
                    Confidence = 95.0F,
                    Value = "An Author",
                }
            },
            Affiliations = new Affiliation[]
            {
                new Affiliation()
                {
                    Confidence = 95.0F,
                    Value = "Affiliation"
                }
            },
            JournalTitle = new JournalTitle()
            {
                Confidence = 95.0F,
                Value = "--JOURNAL TITLE--"
            },
            Doi = new Doi()
            {
                Confidence = 95.0F,
                Value = "-- DOI --"
            },
            IssueNumber = new IssueNumber()
            {
                Confidence = 95.0F,
                Value = "--ISSUE NUMBER--"
            },
            Volume = new Volume()
            {
                Confidence = 95.0F,
                Value = "--VOLUME--"
            },
            Issn = new Issn()
            {
                Confidence = 95.0F,
                Value = "--ISSN--"
            },
            Year = new Year()
            {
                Confidence = 95.0F,
                Value = "--Year--"
            },
            Pages = new Pages()
            {
                Confidence = 95.0F,
                Value = "--PAGES--"
            },
            CountryOfOccurrence = new CountryOfOccurrence()
            {
                Confidence = 95.0F,
                Value = "--COUNTRY--"
            },
            Keywords = new Keywords()
            {
                Confidence = 95.0F,
                Value = "--KEYWORDS--"
            }
        };

        return extractedReference;
    }
    private CompositeQualityChecker CreateValidator()
    {
        _countryRepository.Setup(x => x.GetNames()).ReturnsAsync(new List<string>
        {
            "--COUNTRY--"
        });
        _journalRepository.Setup(x => x.GetNames()).ReturnsAsync(new List<string>
        {
          "--JOURNAL TITLE--"
        });
        return new CompositeQualityChecker(_mockLogger.Object, new List<IExtractionValidator>
        {
            new MandatoryFieldsValueChecker(_mockLoggerFieldsValueChecker.Object),
            new MandatoryFieldsConfidenceChecker(_mockLoggerMandatoryFieldsConfidenceChecker.Object,_configuration),
            new NonMandatoryFieldsConfidenceChecker(_mockLoggerNonMandatoryFieldsConfidenceChecker.Object, _configuration),
            new OverallWeightedConfidenceChecker(_mockLoggerWeightedConfidenceChecker.Object, _configuration),
        }, new List<IExtractionMatchValidator>
        {
           new CountryOfOccurrenceChecker(_mockLoggerCountryOfOccurrenceChecker.Object, _countryRepository.Object, _configuration),
           new JournalTitleChecker(_mockLoggerJournalTitleChecker.Object,_journalRepository.Object, _configuration)
        });
    }
}
