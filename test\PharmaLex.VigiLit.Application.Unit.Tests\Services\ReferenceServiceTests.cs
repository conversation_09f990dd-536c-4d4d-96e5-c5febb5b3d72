﻿using AutoMapper;
using Moq;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Application.Services;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Services;

public class ReferenceServiceTests
{
    private readonly Mock<IReferenceClassificationRepository> _mockReferenceClassificationRepository = new();
    private readonly Mock<IReferenceRepository> _mockReferenceRepository = new();
    private readonly Mock<ISubstanceRepository> _mockSubstanceRepository = new();
    private readonly Mock<ISpecialSituationService> _mockSpecialSituationService = new();
    private readonly IReferenceService _referenceService;
    private readonly IMapper _mapper;

    private static bool ArrayIsInAlphabeticalOrder(string?[] array) => array.Zip(array.Skip(1), (a, b) => string.Compare(a?.Trim(), b?.Trim())).All(x => x == -1 || x == 0);
    private static bool AdditionalFieldListValuesSame(List<SpecialSituationModel> list1, IEnumerable<SpecialSituationModel> list2) => list1.Zip(list2, (a, b) => a.Id == b.Id && a.Name == b.Name).All(equal => equal);

    private const int _matchingReferenceId = 1;
    private const int _nonMatchingReferenceId = 99;

    public ReferenceServiceTests()
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ReferenceMappingProfile());
        });

        _mapper = mapperConfig.CreateMapper();

        _referenceService = new ReferenceService(
            _mockReferenceRepository.Object,
            _mapper,
            _mockReferenceClassificationRepository.Object,
            _mockSubstanceRepository.Object,
            _mockSpecialSituationService.Object);
    }

    [Fact]
    public async Task GetAllAsync_Returns_Correct_References()
    {
        // Arrange
        _mockReferenceRepository.Setup(x => x.GetAllAsync()).ReturnsAsync(DummyRepositoryReferences());

        // Act
        var result = await _referenceService.GetAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count());
        Assert.Equal("1", result.ToList()[0].SourceId);
        Assert.Equal("Title 1", result.ToList()[0].Title);
        Assert.Equal("Abstract 1", result.ToList()[0].Abstract);
    }

    [Fact]
    public async Task GetReferenceByIdAsync_Returns_CorrectReference_When_ReferenceFound()
    {
        // Arrange
        _mockReferenceRepository.Setup(x => x.GetReferenceById(1)).Returns(SingleDummyRepositoryReference);

        // Act
        var result = await _referenceService.GetReferenceByIdAsync(_matchingReferenceId);

        // Assert
        Assert.Equal("1", result.SourceId);
        Assert.Equal("Title 1", result.Title);
        Assert.Equal("Abstract 1", result.Abstract);
    }

    [Fact]
    public async Task GetReferenceByIdAsync_Returns_NoReference_When_NoReferenceMatchFound()
    {
        // Arrange
        _mockReferenceRepository.Setup(x => x.GetReferenceById(1)).Returns(SingleDummyRepositoryReference);

        // Act
        var result = await _referenceService.GetReferenceByIdAsync(_nonMatchingReferenceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetReferenceDetails_Returns_PageModel_When_ReferenceMatchFound()
    {
        // Arrange
        _mockReferenceRepository.Setup(x => x.GetReferenceById(1)).Returns(SingleDummyRepositoryReference);
        _mockReferenceClassificationRepository.Setup(x => x.GetForReferenceDetails(1, new User())).Returns(DummyReferenceClassificationsWithSubstanceModel);

        // Act
        var result = await _referenceService.GetReferenceDetails(_matchingReferenceId, new User());

        // Assert
        Assert.Equal("Title 1", result.Reference.Title);
    }

    [Fact]
    public async Task GetSplitReferenceDetails_Returns_NoPageModel_When_NoReferenceMatchFound()
    {
        // Arrange
        _mockReferenceRepository.Setup(x => x.GetReferenceById(1)).Returns(SingleDummyRepositoryReference);
        _mockReferenceClassificationRepository.Setup(x => x.GetForReferenceDetails(1, new User())).Returns(DummyReferenceClassificationsWithSubstanceModel);

        // Act
        var result = await _referenceService.GetReferenceDetails(_nonMatchingReferenceId, new User());

        // Assert
        Assert.Null(result.Reference);
    }

    [Fact]
    public async Task GetSplitReferenceDetails_Returns_PageModelWithPotentialCase_When_ReferenceMatch()
    {
        // Arrange
        _mockReferenceRepository.Setup(x => x.GetReferenceById(1)).Returns(SingleDummyRepositoryReference);
        _mockSubstanceRepository.Setup(x => x.GetActiveSubstancesWithNoClassificationsForReference(1)).Returns(Task.FromResult(DummyRepositorySubstances()));
        _mockSpecialSituationService.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(GetSpecialSituations()));

        // Act
        var result = await _referenceService.GetSplitReferenceDetails(_matchingReferenceId);

        // Assert
        Assert.Equal("Title 1", result.Reference.Title);
        Assert.True(result.ReferenceClassification.ClassificationCategoryId == (int)ClassificationCategoriesType.PotentialCase);
    }

    [Fact]
    public async Task GetSplitReferenceDetails_Returns_PageModelWithOrderedSubstances_When_ReferenceMatch()
    {
        // Arrange
        _mockReferenceRepository.Setup(x => x.GetReferenceById(1)).Returns(SingleDummyRepositoryReference);
        _mockSubstanceRepository.Setup(x => x.GetActiveSubstancesWithNoClassificationsForReference(1)).Returns(Task.FromResult(DummyRepositorySubstances()));
        _mockSpecialSituationService.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(GetSpecialSituations()));

        // Act
        var result = await _referenceService.GetSplitReferenceDetails(_matchingReferenceId);

        // Assert
        Assert.True(ArrayIsInAlphabeticalOrder(result.Substances.Select(x => x.Name).ToArray()));
    }

    [Fact]
    public async Task GetSplitReferenceDetails_Returns_PageModelWith_SpecialSituations_When_ReferenceMatch()
    {
        // Arrange
        _mockReferenceRepository.Setup(x => x.GetReferenceById(1)).Returns(SingleDummyRepositoryReference);
        _mockSubstanceRepository.Setup(x => x.GetActiveSubstancesWithNoClassificationsForReference(1)).Returns(Task.FromResult(DummyRepositorySubstances()));
        _mockSpecialSituationService.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(GetSpecialSituations()));

        // Act
        var result = await _referenceService.GetSplitReferenceDetails(_matchingReferenceId);

        // Assert
        Assert.True(AdditionalFieldListValuesSame(GetSpecialSituations().ToList(), result.SpecialSituationList));
    }

    [Fact]
    public async Task GetSplitReferenceDetails_Returns_EmptyPageModel_When_NoReferenceMatch()
    {
        // Arrange
        _mockReferenceRepository.Setup(x => x.GetReferenceById(1)).Returns(SingleDummyRepositoryReference);
        _mockSubstanceRepository.Setup(x => x.GetActiveSubstancesWithNoClassificationsForReference(1)).Returns(Task.FromResult(DummyRepositorySubstances()));
        _mockSpecialSituationService.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(GetSpecialSituations()));

        // Act
        var result = await _referenceService.GetSplitReferenceDetails(_nonMatchingReferenceId);

        // Assert
        Assert.Null(result.Reference);
    }

    [Fact]
    public async Task GetDoIdDetails_Returns_True_When_DoiIdMatch()
    {
        // Arrange
        _mockReferenceRepository.Setup(x => x.DoesDoiExist("10.1002/ece3.5646")).Returns(Task.FromResult(true));

        // Act
        var result = await _referenceService.IsDoiExisting("10.1002/ece3.5646");

        // Assert
        Assert.True(result);

    }

    [Fact]
    public async Task GetDoIdDetails_Returns_False_When_DoiIdNotMatching()
    {
        // Arrange
        _mockReferenceRepository.Setup(x => x.DoesDoiExist("10.007/ece3.5646")).Returns(Task.FromResult(false));

        // Act
        var result = await _referenceService.IsDoiExisting("10.1002/ece3.5646");

        // Assert
        Assert.False(result);

    }

    [Fact]
    public async Task IsDoiValid_ValidDoi_ReturnsTrue()
    {
        // Arrange
        string validDoi = "10.1000/xyz123";

        // Act
        var result = await _referenceService.IsDoiValid(validDoi);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsDoiValid_InvalidDoi_ReturnsFalse()
    {
        // Arrange
        string invalidDoi = "invalid-doi-format";

        // Act
        var result = await _referenceService.IsDoiValid(invalidDoi);

        // Assert
        Assert.False(result);
    }


    [Fact]
    public async Task IsDoiValid_EmptyDoi_ReturnsFalse()
    {
        // Arrange
        string emptyDoi = "";

        // Act
        var result = await _referenceService.IsDoiValid(emptyDoi);

        // Assert
        Assert.False(result);
    }
    private static List<ReferenceModel> DummyRepositoryReferences()
    {
        return new List<ReferenceModel> {
            new() {
                Id = 1,
                SourceId = "1",
                Abstract = "Abstract 1",
                Title = "Title 1"
            },
            new() {
                Id = 2,
                SourceId = "2",
                Abstract = "Abstract 2",
                Title = "Title 2"
            },
            new() {
                Id = 3,
                SourceId = "3",
                Abstract = "Abstract 3",
                Title = "Title 3"
            }
        };
    }

    private static Task<Reference> SingleDummyRepositoryReference()
    {
        var reference = new Reference
        {
            SourceId = "1",
            Abstract = "Abstract 1",
            Title = "Title 1"
        };

        return Task.FromResult(reference);
    }

    // Why is this code not covered? Used in a moc but its never or.. ?

    private static Task<IEnumerable<ReferenceClassificationWithSubstanceModel>> DummyReferenceClassificationsWithSubstanceModel()
    {
        var referenceClassifications = new List<ReferenceClassificationWithSubstanceModel>()
        {
            new() { Id = 1, IsActive = true }
        };

        return Task.FromResult<IEnumerable<ReferenceClassificationWithSubstanceModel>>(referenceClassifications);
    }

    private static IEnumerable<SubstanceItemModel> DummyRepositorySubstances()
    {
        return new List<SubstanceItemModel>
        {
            new() { Id = 1, Name = "Substance 1" },
            new() { Id = 3, Name = "Substance 3" },
            new() { Id = 2, Name = "Substance 2" },
        }.AsEnumerable();
    }
    private static IEnumerable<SpecialSituationModel> GetSpecialSituations()
    {
        return
        [
            new SpecialSituationModel { Id = 1, Name = "Field 1" },
            new SpecialSituationModel { Id = 2, Name = "Field 2" },
            new SpecialSituationModel { Id = 3, Name = "Field 3" },
            new SpecialSituationModel { Id = 4, Name = "Field 4" },
            new SpecialSituationModel { Id = 5, Name = "Field 5" }
        ];
    }

}
