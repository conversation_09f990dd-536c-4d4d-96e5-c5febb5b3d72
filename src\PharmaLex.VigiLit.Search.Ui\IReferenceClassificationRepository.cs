﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Search.Ui.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Search.Ui;

public interface IReferenceClassificationRepository : ITrackingRepository<ReferenceClassification>
{
    Task<IEnumerable<ReferenceClassificationSupportModel>> Search(ClassificationSearchRequest request, User user, int? maxRows);
    Task<IEnumerable<PrintPreviewSearchResultModel>> PrintPreview(ClassificationSearchRequest request, User user, int? maxRows);
}