﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Services;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Service.Clients;
using PharmaLex.VigiLit.Scraping.Service.Import;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Services;
using Phlex.Core.Apify.Extensions;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook;
using System.Reflection;

namespace PharmaLex.VigiLit.Scraping.Service;

public static class ConfigureServices
{
    public static void RegisterScrapingClients(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IVigiLitScrapingClient, VigiLitScrapingClient>();
        services.AddScoped<IRestoreSchedulesCommandHandler, RestoreSchedulesCommandHandler>();
        services.AddScoped<ICreateOrUpdateSchedule<PERSON>ommand<PERSON>and<PERSON>, CreateOrUpdateScheduleCommandHandler>();
    }

    public static void RegisterScrapingServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IEnumerable<AzureStorageDocumentOptions>>(sp =>
        {
            return new List<AzureStorageDocumentOptions>
            {
                sp.GetRequiredService<IOptions<AzureStorageImportScrapeDocumentOptions>>().Value
            };
        });
        services.Configure<AzureStorageImportScrapeDocumentOptions>(configuration.GetSection(AzureStorageImportScrapeDocumentOptions.ConfigurationKey));
        services.AddScoped<IDownloadBlobStorage, DownloadBlobStorage>();
        services.AddScoped<IApifyFileGroupingService, ApifyFileGroupingService>();
        services.AddApify(configuration);
        services.AddScoped<IApifyNotification, ApifyNotification>();

        services.AddScoped<IApifyScheduleService, ApifyScheduleService>();
        services.AddScoped<IApifyWebhookService, ApifyWebhookService>();
        services.AddScoped<IApifyTaskService, ApifyTaskService>();
        services.AddScoped<IScrapingConfigurationService, ScrapingConfigurationService>();

        services.AddSingleton(sp =>
        {
            return new List<ApifyWebhookSettings>
            {
                sp.GetRequiredService<IOptions<ApifyWebhookSettings>>().Value
            };
        });
         services.Configure<ApifyWebhookSettings>(configuration.GetSection(ApifyConfiguration.ConfigKey));

        var assembly = Assembly.Load("Phlex.Core.Apify.Webhook");
        services.AddControllers().AddApplicationPart(assembly);
    }
}