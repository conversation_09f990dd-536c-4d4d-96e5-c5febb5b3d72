﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using System.Reflection;

namespace PharmaLex.VigiLit.DataExtraction.Service.CrossCutting;

public static class CrossCuttingContextExtensions
{
    public static void LogAuditEx<T>(this ICrossCuttingContext<T> ctx, string user, string message) where T : class
    {
        ctx.AuditClient.Send(new StatusChangedEvent(Assembly.GetCallingAssembly().FullName!, Guid.Empty,
            ctx.TimeProvider.GetUtcNow().DateTime, user, message));
    }

    public static void LogInformation<T>(this ICrossCuttingContext<T> ctx, string message) where T : class
    {
        ctx.Logger.LogInformation("{Message}", message);
    }
}