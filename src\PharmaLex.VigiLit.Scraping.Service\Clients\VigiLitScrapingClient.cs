using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Services;

namespace PharmaLex.VigiLit.Scraping.Service.Clients;

public class VigiLitScrapingClient : IVigiLitScrapingClient
{
    private readonly IRestoreSchedulesCommandHandler _restoreSchedulesHandler;
    private readonly ICreateOrUpdateScheduleCommandHandler _createOrUpdateScheduleCommandHandler;

    public VigiLitScrapingClient(
            IRestoreSchedulesCommandHandler restoreSchedulesHandler,
            ICreateOrUpdateScheduleCommandHandler createOrUpdateScheduleCommandHandler
        )
    {
        _restoreSchedulesHandler = restoreSchedulesHandler;
        _createOrUpdateScheduleCommandHandler = createOrUpdateScheduleCommandHandler;
    }

    public async Task<RestoreSchedulesResult> Send(RestoreSchedulesCommand command)
    {
        var serviceResult = await _restoreSchedulesHandler.Consume(command);

        return new RestoreSchedulesResult
        {
            SchedulesCreated = serviceResult.SchedulesCreated,
            TasksCreated = serviceResult.TasksCreated,
            WebhooksCreated = serviceResult.WebhooksCreated,
            JournalsProcessed = serviceResult.JournalsProcessed,
            Errors = serviceResult.Errors,
            Messages = serviceResult.Messages
        };
    }

    public async Task Send(CreateOrUpdateScheduleCommand command)
    {
        await _createOrUpdateScheduleCommandHandler.Consume(command);
    }
}
