﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Auditing.Client;

namespace PharmaLex.VigiLit.DataExtraction.Service.CrossCutting;

public class CrossCuttingContext<T> : ICrossCuttingContext<T> where T : class
{
    public CrossCuttingContext(ILoggerFactory loggerFactory, IAuditClient auditClient, TimeProvider timeProvider, IConfiguration configuration)
    {
        Logger = loggerFactory.CreateLogger<T>();
        AuditClient = auditClient;
        TimeProvider = timeProvider;
        Configuration = configuration;
    }

    public ILogger<T> Logger { get; }
    public IAuditClient AuditClient { get; }
    public TimeProvider TimeProvider { get; }
    public IConfiguration Configuration { get; }
}