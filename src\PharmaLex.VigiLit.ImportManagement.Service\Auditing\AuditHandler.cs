﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.MessageBroker.Contracts;

namespace PharmaLex.VigiLit.ImportManagement.Service.Auditing;

internal class AuditHandler : IAuditHandler
{
    private readonly ILogger<AuditHandler> _logger;

    private readonly IImportingImportRepository _importRepository;

    private readonly TimeProvider _timeProvider;

    public AuditHandler(ILogger<AuditHandler> logger, IImportingImportRepository importRepository, TimeProvider timeProvider)
    {
        _logger = logger;
        _importRepository = importRepository;
        _timeProvider = timeProvider;
    }

    public async Task Consume(StatusChangedEvent command)
    {
        if (command.CorrelationId == Guid.Empty)
        {
            _logger.LogWarning("CorrelationId is empty: {CorrelationId}", command.CorrelationId);
        }

        try
        {
            var importRecord = await _importRepository.GetByCorrelationId(command.CorrelationId);
            _logger.LogInformation("Import Status: {ImportStatus} for CorrelationId : {CorrelationId}", command.Message,
                command.CorrelationId);

            if (ShouldAssignEndDate(command.Message))
            {
                var currentDate = _timeProvider.GetUtcNow().UtcDateTime;
                importRecord.EndDate = currentDate;
                importRecord.ImportDate = currentDate;
            }

            importRecord.Message = command.Message;
            importRecord.LastUpdatedBy = command.User;
            await _importRepository.SaveChangesAsync();
        }
       
        catch (InvalidOperationException ex)
        {
            _logger.LogError(ex, "Correlation Id: {CorrelationId} not found or multiple with same Id", command.CorrelationId);
        }
    }

    private static readonly List<string> EndDateAssignableMessage =
    [
        "Completed",
        "PhlexVision extraction failed",
        "Completed with failures",
        "Completed with no matches"
    ];

    private static bool ShouldAssignEndDate(string message)
    {
        return EndDateAssignableMessage.Contains(message, StringComparer.OrdinalIgnoreCase);
    }
}