﻿namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;

internal class VisionRequest
{
    public required string DocumentId { get; set; }
    public required string DocumentDownloadUrl { get; set; }
    public required string SuccessCallbackUrl { get; set; }
    public required string StatusCallbackUrl { get; set; }
    public required string ErrorCallbackUrl { get; set; }
    public required string DocumentUploadUrl { get; set; }
    public required string BearerToken { get; set; }
    public required List<string> Stages { get; set; }
    public required List<Stage> AiProcessor { get; set; }
    public required string Priority { get; set; }
    public required string FileExtension { get; set; }
}