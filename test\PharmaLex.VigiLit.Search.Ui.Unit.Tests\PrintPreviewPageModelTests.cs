﻿using PharmaLex.VigiLit.Search.Ui.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Search.Ui.Unit.Tests;

public class PrintPreviewPageModelTests
{
    [Fact]
    public void SetSpecialSituations_sets_list_of_potential_case_additional_information_for_bracketed_list()
    {
        var printPreviewPageModel = new PrintPreviewPageModel
        {
            Classifications = new List<PrintPreviewSearchResultModel>
            {
                new() {ClassificationCategory = "Potential Case", SpecialSituations = "[1][3][5]"}
            }
        };

        // Act
        printPreviewPageModel.SetSpecialSituations("Potential Case", GetSpecialSituations());

        // Assert
        Assert.Equal(3, printPreviewPageModel.Classifications.First().SpecialSituationList.Count);

        Assert.Equal("Field 1", printPreviewPageModel.Classifications.First().SpecialSituationList[0]);
        Assert.Equal("Field 3", printPreviewPageModel.Classifications.First().SpecialSituationList[1]);
        Assert.Equal("Field 5", printPreviewPageModel.Classifications.First().SpecialSituationList[2]);
    }

    [Fact]
    public void SetSpecialSituations_sets_empty_list_of_potential_case_additional_information_for_non_potential_case()
    {
        var printPreviewPageModel = new PrintPreviewPageModel
        {
            Classifications = new List<PrintPreviewSearchResultModel>
            {
                new() {ClassificationCategory = "Safety relevant information", SpecialSituations = null}
            }
        };

        // Act
        printPreviewPageModel.SetSpecialSituations("Potential Case", GetSpecialSituations());

        // Assert
        Assert.Empty(printPreviewPageModel.Classifications.First().SpecialSituationList);
    }

    [Fact]
    public void SetSpecialSituations_sets_empty_list_of_potential_case_additional_information_for_empty_list()
    {
        // Arrange
        var printPreviewPageModel = new PrintPreviewPageModel
        {
            Classifications = new List<PrintPreviewSearchResultModel>
            {
                new() {ClassificationCategory = "Potential Case", SpecialSituations = null}
            }
        };

        // Act
        printPreviewPageModel.SetSpecialSituations("Potential Case", GetSpecialSituations());

        // Assert
        Assert.Empty(printPreviewPageModel.Classifications.First().SpecialSituationList);
    }

    private ICollection<SpecialSituationModel> GetSpecialSituations()
    {
        return
        [
            new SpecialSituationModel { Id = 1, Name = "Field 1" },
            new SpecialSituationModel { Id = 2, Name = "Field 2" },
            new SpecialSituationModel { Id = 3, Name = "Field 3" },
            new SpecialSituationModel { Id = 4, Name = "Field 4" },
            new SpecialSituationModel { Id = 5, Name = "Field 5" }
        ];
    }
}