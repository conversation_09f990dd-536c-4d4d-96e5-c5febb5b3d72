﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PharmaLex.BlobStorage;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Auditing.Client;
using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.DataExtraction.Service.CrossCutting;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision.Factories;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision.MetaDataExtractor;
using PharmaLex.VigiLit.DataExtraction.Service.Repositories;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.ImportManagement.Service;
using PhlexVisionTestHarness.CrossCutting;
using PhlexVisionTestHarness.WebApplicationExtensions;
using System.Reflection;
using TextCopy;
using Console = PhlexVisionTestHarness.CrossCutting.ConsoleEx;

namespace PhlexVisionTestHarness;

internal static class Program
{
    private static ServiceProvider? _serviceProvider;

    [STAThread]
    static async Task Main(string[] args)
    {
        IConfiguration configuration = new ConfigurationBuilder()
            .AddUserSecrets(Assembly.GetExecutingAssembly())
            .Build();

        _serviceProvider = GetServiceProvider(configuration);

        StartListener();
        Console.WriteLine(ConsoleColor.Blue, "Press Enter to send request...X to Exit");

        while (Console.ReadLine()?.ToUpper() != "X")
        {
            await CallPhlexVisionService();
            Console.WriteLine(ConsoleColor.Blue, "Waiting for response... Press Enter to send another request...X to Exit");
        }
    }

    private static ServiceProvider GetServiceProvider(IConfiguration configuration)
    {
        var services = new ServiceCollection();
        services.AddHttpClient();
        services.RegisterImportManagementClient();
        services.RegisterImportManagement(configuration);
        services.AddSingleton(configuration);
        services.AddScoped<PlxDbContext, VigiLitDbContext>();
        services.RegisterDbContext<VigiLitDbContext>();
        services.AddScoped<IUserContext, TestUserContext>();
        services.AddScoped<IVigiLitUserContext, TestUserContext>();
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddScoped<IAuditClient, LightWeightAuditClient>();
        services.AddScoped<IMdeQueueItemRepository, MdeQueueItemRepository>();
        services.AddScoped<IPhlexVisionService, PhlexVisionService>();
        services.AddScoped(typeof(ICrossCuttingContext<>), typeof(CrossCuttingContext<>));

        // Comment out the ones you don't want to use
        services.AddScoped<IDocumentProcessModelFactory, SinglePromptDocumentProcessModelFactory>();
        services.AddScoped<IMetaDataExtractor, SinglePromptMetaDataExtractor>();

        services.AddScoped<IDocumentProcessModelFactory, MultiPromptDocumentProcessModelFactory>();
        services.AddScoped<IMetaDataExtractor, MultiPromptMetaDataExtractor>();
        // Comment out the ones you don't want to use

        services.AddSingleton(TimeProvider.System);
        services.AddLogging(configure =>
        {
            configure.AddConsole();
        });

        services.RegisterDocumentBlobServices(configuration);

        return services.BuildServiceProvider();
    }

    private static void StartListener()
    {
        _ = Task.Run(static () =>
            {
                var builder = WebApplication.CreateBuilder();
                var app = builder.Build();
                app.Run(_serviceProvider!);
            }
        );
    }

    private static async Task CallPhlexVisionService()
    {
        var phlexVisionService = _serviceProvider!.GetService<IPhlexVisionService>()!;
        var correlationId = Guid.NewGuid();

        ConsoleEx.WriteLine(ConsoleColor.Cyan, $"CorrelationId available from the clipboard to paste into New Relic: {correlationId}");
        await ClipboardService.SetTextAsync(correlationId.ToString());

        await phlexVisionService.RequestDataExtraction(new ExtractRequest
        {
            CorrelationId = correlationId,
            BatchId = "VigiLit",
            FileName = "SOME_FILE_NAME.PDF"
        });
    }
}