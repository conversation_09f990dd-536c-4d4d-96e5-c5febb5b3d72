﻿using System.ComponentModel.DataAnnotations;

namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class ReferenceClassificationModel
{
    [Required]
    public int Id { get; set; }
    public int ReferenceId { get; set; }
    public int SubstanceId { get; set; }
    [Required]
    [MaxLength(100)]
    public string DosageForm { get; set; }
    public string MinimalCriteria { get; set; }
    [MaxLength(200)]
    public string CountryOfOccurrence { get; set; }
    public string PSURRelevanceAbstract { get; set; }
    public string PvSafetyDatabaseId { get; set; }
    [Required]
    public int ClassificationCategoryId { get; set; }
    public string ClassificationCategoryName { get; set; }
    [MaxLength(256)]
    public string ReasonForChange { get; set; }
    public int ReferenceState { get; set; }
    public string ReferenceStateText { get; set; }
    public bool IsActive { get; set; }
    public string PreclassifierName { get; set; }
    public string SpecialSituations { get; set; }
}
