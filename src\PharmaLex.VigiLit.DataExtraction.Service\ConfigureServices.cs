﻿using Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Services;
using PharmaLex.Core.Configuration.Services;
using PharmaLex.VigiLit.Auditing.Client;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.DataExtraction.Import;
using PharmaLex.VigiLit.DataExtraction.Service.Clients;
using PharmaLex.VigiLit.DataExtraction.Service.CrossCutting;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision.Factories;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision.MetaDataExtractor;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using PharmaLex.VigiLit.DataExtraction.Service.Repositories;

namespace PharmaLex.VigiLit.DataExtraction.Service;

public static class ConfigureServices
{
    public static void RegisterDataExtractionClient(this IServiceCollection services)
    {
        services.AddScoped<IDataExtractionClient, DataExtractionClient>();
        services.AddScoped<IExtractDataCommandHandler, ExtractDataCommandHandler>();
    }

    public static void RegisterDataExtractionServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IDataExtractionService, DataExtractionService>();
        services.AddScoped<IMdeQueueItemRepository, MdeQueueItemRepository>();
        services.AddScoped<IReferenceRepository, ReferenceRepository>();

        var serviceProvider = services.BuildServiceProvider();
        var featureService = serviceProvider.GetService<IFeatureFlagServices>();

        var isMultiPromptEnabled = Task.Run(() => featureService!.GetFeatureFlag("EnableMultiPromptExecution")).Result;

        if (isMultiPromptEnabled!.Enabled)
        {
            services.AddScoped<IDocumentProcessModelFactory, MultiPromptDocumentProcessModelFactory>();
            services.AddScoped<IMetaDataExtractor, MultiPromptMetaDataExtractor>();
        }
        else
        {
            services.AddScoped<IDocumentProcessModelFactory, SinglePromptDocumentProcessModelFactory>();
            services.AddScoped<IMetaDataExtractor, SinglePromptMetaDataExtractor>();
        }

        services.AddScoped<IPhlexVisionService, PhlexVisionService>();
        services.AddScoped(typeof(ICrossCuttingContext<>), typeof(CrossCuttingContext<>));

        services.AddScoped<ICallbackHandler, CallbackHandler>();
        services.AddScoped<IExtractDataCommandHandler, ExtractDataCommandHandler>();
        services.AddScoped<IJournalRepository, JournalRepository>();
        services.AddScoped<ICountryRepository, CountryRepository>();

        services.AddScoped<IDataExtractionProfile, DataExtractionProfile>();
        services.AddScoped<IExtractionValidator, MandatoryFieldsValueChecker>();
        services.AddScoped<IExtractionValidator, MandatoryFieldsConfidenceChecker>();
        services.AddScoped<IExtractionValidator, NonMandatoryFieldsConfidenceChecker>();
        services.AddScoped<IExtractionValidator, OverallWeightedConfidenceChecker>();

        services.AddScoped<IExtractionMatchValidator, JournalTitleChecker>();
        services.AddScoped<IExtractionMatchValidator, CountryOfOccurrenceChecker>();

        services.AddScoped<ICompositeQualityChecker, CompositeQualityChecker>();

        services.RegisterAuditClient();

        var assembly = typeof(ConfigureServices).Assembly;
        services.AddAutoMapper(assembly);

        services.AddControllers().AddApplicationPart(assembly);

        services.Configure<MvcRazorRuntimeCompilationOptions>(options =>
        { options.FileProviders.Add(new EmbeddedFileProvider(assembly)); });

        services.AddSingleton<IEnumerable<AzureStorageDocumentOptions>>(sp =>
        {
            return new List<AzureStorageDocumentOptions>
            {
                sp.GetRequiredService<IOptions<AzureStorageImportFileDocumentUploadOptions>>().Value,
                sp.GetRequiredService<IOptions<AzureStorageImportFileDocumentOptions>>().Value
            };
        });
        services.Configure<AzureStorageImportFileDocumentUploadOptions>(configuration.GetSection(AzureStorageImportFileDocumentUploadOptions.ConfigurationKey));
        services.Configure<AzureStorageImportFileDocumentOptions>(configuration.GetSection(AzureStorageImportFileDocumentOptions.ConfigurationKey));
        services.AddScoped<IExtractDataFileDownload, ExtractDataFileDownload>();
    }
}