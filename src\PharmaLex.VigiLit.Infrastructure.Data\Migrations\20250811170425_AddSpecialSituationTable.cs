﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddSpecialSituationTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SpecialSituations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SpecialSituations", x => x.Id);
                });


            migrationBuilder.Sql("SET IDENTITY_INSERT [dbo].[SpecialSituations] ON; " +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 1,'Use of medicinal product during pregnancy or breastfeeding', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 2,'Paternal exposure to use of medicinal products in pediatrics', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 3,'Use of medicinal products in elderly populations', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 4,'Overdose', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 5,'Abuse', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 6,'Off-label use', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 7,'Misuse', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 8,'Medication error', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 9,'Occupational exposure', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 10,'Lack of therapeutic efficacy', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 11,'Suspected transmission of infectious agent', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 12,'Drug interactions', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 13,'Unexpected benefit', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "INSERT INTO [dbo].[SpecialSituations] ([Id], [Name], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 14,'Counterfeit', GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script'" +

                                 "SET IDENTITY_INSERT [dbo].[SpecialSituations] OFF; ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SpecialSituations");
        }
    }
}
