
services:
  vigilit-ai:
    image: vigilit-ai
    build:
      context: .
      dockerfile: src\PharmaLex.VigiLit.AiAnalysis.Service\Dockerfile

    depends_on:
      rabbitmq:
        condition: service_healthy
      sqlserver:
        condition: service_healthy
      azurite:
        condition: service_healthy

  rabbitmq:
    image: rabbitmq:4.0.9-management-alpine
    environment:
      - RABBITMQ_DEFAULT_USER=test
      - RABBITMQ_DEFAULT_PASS=test
    container_name: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    healthcheck:
      test: rabbitmq-diagnostics -q ping
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s

  sqlserver:
    image: pharmalex/mssql/server
    volumes:
      - c:/volumes/mssql:/var/opt/mssql/data
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=Password1.
      - MSSQL_PID=Enterprise
    container_name: sqlserver
    ports:
      - 1433:1433
    restart: always
    healthcheck:
      test: /opt/mssql-tools18/bin/sqlcmd -C -S localhost -U sa -P Password1. -Q "SELECT 1" -b -o /dev/null
      interval: 10s
      retries: 10
      start_period: 10s
      timeout: 3s

  azurite:
    image: mcr.microsoft.com/azure-storage/azurite:latest
    volumes:
        - c:/volumes/azurite:/data
    container_name: azurite
    command: "azurite -l /data --blobHost 0.0.0.0 --skipApiVersionCheck"
    ports:
      - 10000:10000
      - 10001:10001
      - 10002:10002
    healthcheck: 
      test: nc 127.0.0.1 10000 -z
      interval: 1s
      retries: 30

    