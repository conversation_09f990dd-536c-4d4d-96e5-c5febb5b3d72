﻿@using PharmaLex.VigiLit.Domain.Interfaces.Services;
@using Microsoft.AspNetCore.Authorization;
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Domain.Enums
@using PharmaLex.VigiLit.Domain.UserManagement;
@using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule
@inject IClassificationCategoryService classificationCategoryService
@inject ICountryService countryService
@inject IAuthorizationService AuthorizationService

@model PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule.ReferenceHistoryDetailsPageModel

@{
    ViewData["Title"] = "Classification History";
    IEnumerable<ClassificationCategoryModel> _classificationCategories = await classificationCategoryService.GetAllAsync();
    IEnumerable<string> _countries = await countryService.GetAllAsync();
}

@if (ViewBag.error != null)
{
    <div class="sub-header">
        <h2>Classification History </h2>
        <div class="controls">
            <a class="button" onclick="history.back()">Back</a>
        </div>
    </div>
    <div class="notification warning" style="position:relative; z-index:auto;">
        <div class="icon" style="pointer-events: none;">
            <i class="material-icons">close</i>
        </div>
        <div class="message">
            <h3 class="mb-1">No Actions Found!</h3>
            <p> @ViewBag.error</p>
        </div>
    </div>

}
<div id="app" v-cloak>
    <div class="sub-header">
        <h2>Classification History </h2>
        <h2 class="brand-color">{{referenceClassification.substance.name}}</h2>
        <div class="controls">
            @if ((await AuthorizationService.AuthorizeAsync(User, Policies.CaseFileOperator)).Succeeded)
            {
                <a class="button" :href="'/References/Split/'+ reference.id">Split Reference</a>
            }
            <a class="button" :href="'/References/'+ reference.id">Go To Reference</a>
            <a class="button" :href="'/Search'">Go to Search</a>
            @if ((await AuthorizationService.AuthorizeAsync(User, Policies.InternalSupport)).Succeeded)
            {
                <button class="button" @@click.prevent='checkEdit' :title="getReasonForNonEdit()" :disabled="isDisabled()">{{isEdit ? 'Cancel' : 'Edit'}}</button>
            }
        </div>

    </div>
    <div v-cloak id="reference-details-container" class="flex gapped">
        @Html.AntiForgeryToken()
        <div id="reference-history-section" class="flex-item tile" v-if="!isEdit" :class="'flex-25-percent'">
            <div>
                <h3>History</h3>
                <div v-for="(action, index) in historyActions" @@click="referenceHistoryActionClicked(action, index)"
                     :class="['support-lozenge', action === selectedHistoryAction ? 'selected' : 'selectable']" style="padding:5px;">
                    <div class="lozenge-container">
                        <span :class="`state-indicator-${action.referenceHistoryActionTypeText.toLowerCase().replace(' ', '-')}`">{{action.referenceHistoryActionTypeText}}</span>
                    </div>
                    <div class="action-container">
                        <span><b>{{ getDateFormat(action.timeStamp)}}</b></span><br />
                        <span>by <b>{{action.userName}}</b></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex-item tile reference-details-section" :class="[!isEdit ? 'flex-75-percent' : '']">
            <div id="reference-details-info">
                <label>PLX ID:</label>
                <span>{{referenceClassificationToViewOnly.id}}</span>

                <label>Source Id:</label>
                <span v-if="pubmedSource"><a :href="`https://pubmed.ncbi.nlm.nih.gov/${reference.sourceId}`" target="_blank" rel="noopener">{{reference.sourceId}}</a></span>
                <span v-else>{{reference.sourceId}}</span>

                <label>DOI:</label>
                <span v-if="reference.doi">{{reference.doi}}</span>
                <span v-else>N/A</span>

                <label>Modified:</label>
                <span v-if="pubmedSource">{{getDateFormat(reference.dateRevised)}} <abbr title="Eastern Time">ET</abbr></span>
                <span v-else>N/A</span>
            </div>
            <div class="flex flex-wrap">
                <div id="reference-details-content" class="flex-item flex-60-percent tile  flex-grow-1">
                    <div v-if="previousReference">
                        <reference :reference="reference"
                                   :reference-update="reference"
                                   :reference-snapshot="previousReference"
                                   :substance="substance">
                        </reference>
                    </div>
                    <div v-else>
                        <reference :reference="reference"
                                   :substance="substance">
                        </reference>
                    </div>
                </div>
                <div id="classification-section" v-if="!inActive">
                    <div class="flex-item tile">
                        <div v-if="!isEdit">
                            <h2>Classification</h2>
                            <classification-display v-model:reference-classification="referenceClassification"
                                                    :special-situations="specialSituationsForDisplay">
                            </classification-display>
                        </div>
                        <div v-else>
                            <div class="reference-classification">
                                <classification-form v-model:reference-classification="referenceClassification"
                                                     :classification-categories="classificationCategories"
                                                     :countries="countries"
                                                     :classification-mode="'referenceHistory'"
                                                     :is-country-of-occurrence-verified="true"
                                                     :show-special-situations="true"
                                                     :special-situations="specialSituationsForEdit"
                                                     v-on:reapprove="showConfirm(true)"
                                                     header="Classification">
                                </classification-form>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div id="support-history-modal">
        <modal-dialog v-if="showConfirmDialog"
                      :title="confirmTitle"
                      width="400px"
                      height="150px"
                      v-on:close="showConfirm(false)"
                      v-on:confirm="reclassification">
            <p>You are making an update to this reference. Please give the reason below.</p>
            <label for="reasonForChange">Reason</label>
            <textarea id="reasonForChange" type="text" v-model="reasonForChange" maxlength="256"></textarea>
        </modal-dialog>

        <modal-dialog v-if="confirmLoseChanges"
                      width="300px"
                      height="150px"
                      :title="confirmLoseChangesTitle"
                      v-on:close="rejectLoseChanges"
                      v-on:confirm="confirmLoseChanges">
            <p>You are currently editing a classification, but exiting this tab will cause any changes to be lost. Do you wish to continue?</p>
        </modal-dialog>
    </div>

</div>
@section Scripts {
    <script type="text/javascript">

        let model = @Html.Raw(AntiXss.ToJson(Model));
        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        var pageConfig = {
            appElement: "#app",
            data: function () {
                return {
                    reference: model.referenceDetails,
                    previousReference: model.previousReferenceDetails,
                    historyActions: model.historyActions,
                    referenceClassification: model.referenceClassification,
                    referenceClassificationToViewOnly: { ...model.referenceClassification },
                    substance: model.referenceClassification.substance,
                    classificationCategories: @Html.Raw(AntiXss.ToJson(_classificationCategories)),
                    countries: @Html.Raw(AntiXss.ToJson(_countries)),
                    selectedHistoryAction: null,
                    selectedHistoryActionIndex: 0,
                    previousHistoryAction: null,
                    isEdit: false,
                    inActive: false,
                    confirmReason: null,
                    confirmTitle: 'Confirm your update',
                    showConfirmDialog: null,
                    confirmLoseChangesTitle: 'Cancel edit',
                    confirmLoseChanges: null,
                    rejectLoseChanges: null,
                    reasonForChange: '',
                    isEditable: model.isEditable,
                    reasonForNotEditable: '',
                    specialSituationsForEdit: {
                        listItems: [],
                        selectedIds: []
                    },
                    specialSituationsForDisplay: model.specialSituationList
                };
            },
            methods: {
                getDateFormat: function (date) {
                    return moment.utc(date).format('DD MMM YYYY');
                },
                checkEdit() {
                    this.isEdit = !this.isEdit;
                },
                checkIfLatestActionIsInactive() {
                    return this.historyActions.some(action => action.referenceHistoryActionType === 4) ? true : false;
                },
                showConfirm(show) {
                    this.showConfirmDialog = show;
                },
                isDisabled() {
                    if (this.checkIfLatestActionIsInactive() || !this.isEditable || this.selectedHistoryActionIndex != 0) {
                        return true;
                    } else {
                        return false;
                    }
                },
                getReasonForNonEdit() {
                    if (this.checkIfLatestActionIsInactive()) {
                        return this.reasonForNotEditable = "Inactive duplicate: Reference has been identified as a duplicate and can't be edited.";
                    }

                    else if (!this.isEditable) {
                        return this.reasonForNotEditable = "Being looked at by Pre/master assessors: Classification is being reviewed by the assessment team and can't be edited";
                    }

                    else if (this.selectedHistoryActionIndex != 0) {
                        return this.reasonForNotEditable = "Current row not selected: Select the current classification in order to edit";
                    }
                },
                referenceHistoryActionClicked: function (action, index) {
                    this.selectedHistoryAction = action;
                    this.selectedHistoryActionIndex = index;
                    if (index < this.historyActions.length - 1) {
                        this.previousHistoryAction = this.historyActions[index + 1];
                    } else {
                        this.previousHistoryAction = null;
                    }
                    this.getReferenceClassificationByAction(action);
                },
                getReferenceClassificationByAction: function (action) {

                    var url = `/References/Classifications/${this.referenceClassification.id}/Action/${action.id}`;

                    if (this.previousHistoryAction) {
                        url = url + `?previousActionId=${this.previousHistoryAction.id}`;
                    }
                    fetch(url, {
                        method: "GET",
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(res => {
                            if (!res.ok) {
                                throw res;
                            }
                            return res.json();
                        })
                        .then(data => {
                            this.loadModel(data);
                        })
                        .catch(error => {
                            console.log(error);
                            plx.toast.show('Something went wrong, please try again.', 2, 'failed', null, 2500);
                        })

                },
                loadModel: function (data) {
                    this.reference = data.referenceDetails;
                    this.previousReference = data.previousReferenceDetails;
                    this.referenceClassification = data.referenceClassification;
                },
                reclassification() {
                    if (this.reasonForChange.length == 0) {
                        plx.toast.show('Please provide a reason for change.', 2, 'confirm', null, 2500);
                        return;
                    }

                    this.referenceClassification.reasonForChange = this.reasonForChange;
                    fetch(`/References/ReClassify`, {
                        method: "POST",
                        credentials: 'same-origin',
                        body: JSON.stringify({
                            Id: this.referenceClassification.id,
                            DosageForm: this.referenceClassification.dosageForm,
                            CountryOfOccurrence: this.referenceClassification.countryOfOccurrence,
                            ClassificationCategoryId: this.referenceClassification.classificationCategoryId,
                            ReasonForChange: this.referenceClassification.reasonForChange,
                            MinimalCriteria: this.referenceClassification.minimalCriteria,
                            PSURRelevanceAbstract: this.referenceClassification.psurRelevanceAbstract,
                            PvSafetyDatabaseId: this.referenceClassification.pvSafetyDatabaseId,
                            SpecialSituations: this.getFormattedSpecialSituations(this.referenceClassification)
                        }),
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        },
                    }).then(result => {
                        if (result.ok) {
                            plx.toast.show('Classification has been updated.', 2, 'confirm', null, 2500);
                            setTimeout(() => {
                                this.isEdit = false;
                                window.location.reload();
                            }, 2500);
                        }
                        else {
                            plx.toast.show('There was a problem updating the classification. Please try again.', 2, 'failed', null, 5000)
                            console.log(error);
                        }

                        this.showConfirm(false);
                    })
                },
                formatBracketNumbers(arr) {
                    return arr.sort((a, b) => a - b).map(x => `[${x}]`).join('');
                },
                parseBracketedNumbers: function (str) {
                    return str
                        .slice(1, -1)
                        .split("][")
                        .map(Number);
                },
                isPotentialCase(referenceClassification) {
                    return referenceClassification.classificationCategoryId == 1;
                },
                getFormattedSpecialSituations(referenceClassification) {
                    return this.isPotentialCase(referenceClassification) ? this.formatBracketNumbers(this.specialSituationsForEdit.selectedIds) : '';
                }
            },
            watch: {
                selectedHistoryAction(newValue) {
                    // Check if action is Inactive
                    this.inActive = newValue.referenceHistoryActionType === 4 ? true : false;
                }
            },
            computed: {
                pubmedSource() {
                    return this.reference && this.reference.sourceSystem == '@((Int16)SourceSystem.PubMed)';
                }
            },
            created() {

                if (model.specialSituationList) {
                    this.specialSituationsForEdit.listItems = [...model.specialSituationList];

                    if (this.isPotentialCase(model.referenceClassification) && model.referenceClassification.specialSituations) {
                        this.specialSituationsForEdit.selectedIds = this.parseBracketedNumbers(model.referenceClassification.specialSituations);
                    }
                }if (this.historyActions.length > 0) {
                    this.selectedHistoryAction = this.historyActions[0];
                }
                if (this.historyActions.length > 1) {
                    this.previousHistoryAction = this.historyActions[1];
                }
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/ReferenceComponent" />
    <partial name="Components/ClassificationDisplay" />
    <partial name="Components/ClassificationForm" />
    <partial name="Components/ModalDialog" />
    <partial name="Components/MultiSelect" />
}
