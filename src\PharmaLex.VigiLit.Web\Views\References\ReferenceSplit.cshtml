﻿@using PharmaLex.VigiLit.Domain.Interfaces.Services;
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule

@inject ICountryService _countryService
@inject IClassificationCategoryService _classificationCategoryService

@model PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule.ReferenceSplitPageModel

@{
    ViewData["Title"] = "Split Reference";
    IEnumerable<string> _countries = await _countryService.GetAllAsync();
    IEnumerable<ClassificationCategoryModel> _classificationCategories = await _classificationCategoryService.GetAllAsync();
}

<div id="split-references" v-cloak>
    <div class="sub-header">
        <h2>Reference</h2>
        <h2>
            Source Id
            <a :href="`https://pubmed.ncbi.nlm.nih.gov/${reference.sourceId}`" target="_blank" rel="noopener">{{reference.sourceId}}</a>
        </h2>

        <div class="controls">
            <a class="button" :href="/References/+ reference.id">Back To Reference</a>
            <button type="button" :disabled="!enableSave" class="button" @@click="confirmSave">Save</button>
        </div>

    </div>
    <div v-cloak id="reference-details-container">
        @Html.AntiForgeryToken()
        <div class="flex-item tile reference-details-section flex-75-percent">
            <div class="flex flex-wrap">
                <div id="reference-details-content" class="flex-item flex-60-percent tile  flex-grow-1">
                    <reference :reference="reference"
                               :substance="substance">
                    </reference>
                </div>
                <div id="classification-section">
                    <div class="flex-item tile">
                        <div class="reference-classification">
                            <split-reference-classification-form v-model:reference-classification="referenceClassification"
                                                                 :classification-categories="classificationCategories"
                                                                 :substances="substances"
                                                                 :countries="countries"
                                                                 :show-special-situations="true"
                                                                 :special-situation-list="specialSituationList"
                                                                 :is-country-of-occurrence-verified="true"
                                                                 v-on:valid="enableSaveButton"
                                                                 header="Classification">
                            </split-reference-classification-form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="support-history-modal">
        <modal-dialog v-if="showConfirmSaveDialog"
                      width="300px"
                      height="150px"
                      :title="confirmSaveTitle"
                      v-on:close="rejectSave"
                      v-on:confirm="save">
            <p>Are you sure you wish to save this split reference?</p>
        </modal-dialog>
    </div>
</div>

@section Scripts {
    <script type="text/javascript">

        let model = @Html.Raw(AntiXss.ToJson(Model));
        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        var pageConfig = {
            appElement: "#split-references",
            data: function () {
                return {
                    reference: model.reference,
                    referenceClassification: model.referenceClassification,
                    substances: model.substances,
                    specialSituationList: {
                        listItems: [],
                        selectedIds: []
                    },
                    substance: {},
                    companies: model.companies,
                    classificationCategories: @Html.Raw(AntiXss.ToJson(_classificationCategories)),
                    countries: @Html.Raw(AntiXss.ToJson(_countries)),
                    enableSave: false,
                    showConfirmSaveDialog: false,
                    confirmSaveTitle: "Save Split Reference",
                    userSelected: {
                        referenceId: model.reference.id,
                        isValid: false,
                        companyIds: { selectedIds: [] },
                        substanceIdSelected: '',
                        referenceClassification: {
                            substanceId: '',
                            dosageForm: '',
                            minimalCriteria: '',
                            countryOfOccurrence: '',
                            classificationCategoryId: ''
                        }
                    }
                };
            },
            methods: {
                enableSaveButton(userSelected) {
                    this.enableSave = userSelected.isValid;
                    this.userSelected = {...userSelected};
                    this.userSelected.referenceId = this.reference.id;
                    this.userSelected.selectedCompanyIds = userSelected.companyIds.selectedIds.slice();
                },
                confirmSave() {
                    this.showConfirmSaveDialog = true;
                },
                rejectSave() {
                    this.showConfirmSaveDialog = false;
                },
                save() {
                    this.showConfirmSaveDialog = false;
                    this.userSelected.referenceClassification.specialSituations = this.formatBracketNumbers(this.specialSituationList.selectedIds);

                    fetch(`/References/Split/Save`, {
                        method: "POST",
                        body: JSON.stringify(this.userSelected),
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(() => {
                            plx.toast.show('The reference was split successfully.', 2, 'confirm', null, 2500);
                            window.location.href = "/search";
                        })
                        .catch(error => {
                            plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                        });
                },
                formatBracketNumbers: function (arr) {
                    return arr.sort((a, b) => a - b).map(x => `[${x}]`).join('');
                },
            },
            created() {
                if (model.specialSituationList) {
                    this.specialSituationList.listItems = [...model.specialSituationList];
                }
            }
        };
    </script>
}

@section VueComponentScripts{
    <partial name="Components/ReferenceComponent" />
    <partial name="Components/SplitReferenceClassificationForm" />
    <partial name="Components/MultiSelect" />
    <partial name="Components/ModalDialog" />
}
