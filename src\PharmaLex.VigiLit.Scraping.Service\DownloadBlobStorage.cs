﻿using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Import;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service;

public class DownloadBlobStorage : IDownloadBlobStorage
{
    private readonly IDocumentService _documentService;
    private readonly AzureStorageImportScrapeDocumentOptions _importScrapeDocumentOptions;
    private readonly IBlobContainerClientProvider _blobContainerClientProvider;
    private string _folderName;

    public DownloadBlobStorage(
        IDocumentService documentService,
        IOptions<AzureStorageImportScrapeDocumentOptions> importScrapeDocumentOptions,
        IBlobContainerClientProvider blobContainerClientProvider)
    {
        _documentService = documentService;
        _folderName = string.Empty;
        _importScrapeDocumentOptions = importScrapeDocumentOptions.Value;
        _blobContainerClientProvider = blobContainerClientProvider;
    }

    public void SetBlobFolderName(string folderName)
    {
        _folderName = folderName;
    }

    public async Task<IEnumerable<string>> GetBlobPathsAsync(string folderName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(folderName))
            throw new ArgumentNullException(nameof(folderName));

        var documentDescriptor = GetDocumentDescriptor(folderName);
        var containerClient = _blobContainerClientProvider.Provide(documentDescriptor);
        var prefix = $"scraping/{folderName}/";

        var blobPaths = new List<string>();

        await foreach (BlobItem blobItem in containerClient.GetBlobsAsync(
            prefix: prefix,
            cancellationToken: cancellationToken))
        {
            // Get the full blob path (e.g., "scraping/myfolder/file1.pdf")
            blobPaths.Add(blobItem.Name);
        }

        return blobPaths;
    }

    private DocumentDescriptor GetDocumentDescriptor(string fileName)
    {
        var blobName = $"scraping/{_folderName}/{fileName}";
        return new DocumentDescriptor(_importScrapeDocumentOptions.AccountName, _importScrapeDocumentOptions.ContainerName, blobName);
    }

    public async Task WriteDataItemAsync(string fileName, Stream fileStream, IReadOnlyDictionary<string, string>? metadata = null, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(fileName);

        var blobClient = await _documentService.Create(documentDescriptor, fileStream, cancellationToken);

        if (metadata is not null && metadata.Count > 0)
        {
            await blobClient.SetMetadataAsync(metadata.ToDictionary(), cancellationToken: cancellationToken);
        }
    }
}
