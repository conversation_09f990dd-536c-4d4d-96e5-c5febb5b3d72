﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using PharmaLex.Core.UserManagement;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.VigiLit.Application.ClassMap;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Search.Ui.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Search.Ui.Controllers;

[Authorize]
[Route("[controller]")]
public class SearchController : BaseController
{
    private readonly IExportService _exportService;
    private readonly ISubstanceService _substanceService;
    private readonly IClassificationCategoryService _classificationCategoryService;
    private readonly IUserRepository<User> _userRepository;
    private readonly ISearchService _searchService;
    private readonly ICompanyService _companyService;
    private readonly ISpecialSituationService _specialSituationService;

    public SearchController(
        IExportService exportService,
        ISubstanceService substanceService,
        IClassificationCategoryService classificationCategoryService,
        IUserRepository<User> userRepository,
        ISearchService searchService,
        ICompanyService companyService,
        IUserSessionService userSessionService,
        ISpecialSituationService specialSituationService,
        IConfiguration configuration) : base(userSessionService, configuration)
    {
        _exportService = exportService;
        _substanceService = substanceService;
        _classificationCategoryService = classificationCategoryService;
        _userRepository = userRepository;
        _searchService = searchService;
        _companyService = companyService;
        _specialSituationService = specialSituationService;
    }

    public async Task<IActionResult> Index()
    {
        var securityUser = await _userRepository.GetForSecurity(CurrentUserId);

        var model = new SearchPageModel
        {
            Substances = await _substanceService.GetForSearch(securityUser),
            ClassificationCategories = await _classificationCategoryService.GetAllAsync(),
            Companies = await _companyService.GetForSearch(securityUser),
            DisplayLastUpdatedDateFilter = true,
            SpecialSituations = await _specialSituationService.GetAllAsync(),
        };

        return View(model);
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> ReferenceSearch([FromQuery] string term)
    {
        var model = (await _searchService.ReferenceSearch(term, await _userRepository.GetForSecurity(CurrentUserId)))?.ToList();
        return Ok(model);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> ClassificationSearch([FromBody] ClassificationSearchRequest request)
    {
        var results = await _searchService.ClassificationSearch(request, await _userRepository.GetForSecurity(CurrentUserId), 100);
        return Ok(results);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> Export([FromBody] ClassificationSearchRequest request)
    {
        var data = await _searchService.ClassificationSearch(request, await _userRepository.GetForSecurity(CurrentUserId), 10000);

        var export = _exportService.Export<ReferenceClassificationSupportModel, ReferenceClassificationSupportModelClassMap>("ClassificationSearch", data.Classifications);

        return File(export.Data, export.ContentType, export.FileName);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> PrintPreview([FromForm] string printPreviewRequest)
    {
        var request = JsonConvert.DeserializeObject<ClassificationSearchRequest>(printPreviewRequest);

        var model = await _searchService.PrintPreview(request, await _userRepository.GetForSecurity(CurrentUserId), 5000);

        var records = await _specialSituationService.GetAllAsync();
        var potentialCaseCategory = await _classificationCategoryService.GetByIdAsync(1);

        model.SetSpecialSituations(potentialCaseCategory.Name, records.ToList());

        return View(model);
    }
}