﻿using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Search.Ui.Models;

public class PrintPreviewPageModel
{
    public PrintPreviewFiltersModel Filters { get; set; }

    public IEnumerable<PrintPreviewSearchResultModel> Classifications { get; set; }

    public void SetSpecialSituations(string potentialCaseCategoryName, ICollection<SpecialSituationModel> records)
    {
        foreach (var classification in Classifications)
        {
            if (classification.ClassificationCategory == potentialCaseCategoryName)
            {
                var integers = SplitBracketedNumbers(classification.SpecialSituations);
                if (integers.Count > 0)
                {
                    classification.SpecialSituationList = records.Where(x => integers.Contains(x.Id)).Select(x => x.Name).ToList();
                }
            }
        }
    }

    private static List<int> SplitBracketedNumbers(string input)
    {
        var result = new List<int>();

        if (!string.IsNullOrEmpty(input))
        {
            foreach (var part in input.Split(']'))
            {
                if (string.IsNullOrWhiteSpace(part)) continue;

                string numberPart = part.TrimStart('[');
                if (int.TryParse(numberPart, out int number))
                {
                    result.Add(number);
                }
            }
        }

        return result;
    }
}