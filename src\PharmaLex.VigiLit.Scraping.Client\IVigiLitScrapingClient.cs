using PharmaLex.VigiLit.Scraping.Client.Models;

namespace PharmaLex.VigiLit.Scraping.Client;

public interface IVigiLitScrapingClient
{
    /// <summary>
    /// Sends the specified command to restore schedules
    /// </summary>
    /// <param name="command">The command.</param>
    /// <returns></returns>
    Task<RestoreSchedulesResult> Send(RestoreSchedulesCommand command);

    /// <summary>
    /// Sends the specified command to schedule a web crawl using a list of URLs
    /// </summary>
    /// <param name="command">The command.</param>
    /// <returns></returns>
    Task Send(CreateOrUpdateScheduleCommand command);
}
