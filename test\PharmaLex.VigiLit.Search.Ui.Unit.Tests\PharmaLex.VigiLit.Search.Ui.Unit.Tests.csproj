<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>


	<PropertyGroup>
		<NoWarn>$(NoWarn);CA1859</NoWarn>
	</PropertyGroup>


	<ItemGroup>
		<PackageReference Include="coverlet.collector" Version="6.0.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.5.3" />
		<PackageReference Include="XunitXml.TestLogger" Version="6.1.0" />
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.Search.Ui\PharmaLex.VigiLit.Search.Ui.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Test.Fakes\PharmaLex.VigiLit.Test.Fakes.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Test.Framework\PharmaLex.VigiLit.Test.Framework.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
