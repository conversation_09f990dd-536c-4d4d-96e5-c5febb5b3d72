﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using PharmaLex.VigiLit.ImportManagement.Client;
using Xunit;

namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests
{

    public class CountryOfOccurrenceMatchingCheckerTests
    {
        private readonly CountryOfOccurrenceChecker _countryOfOccurrenceMatchingChecker;
        private readonly Mock<ILogger<CountryOfOccurrenceChecker>> _mockLogger = new();
        private readonly Mock<ICountryRepository> _countriesRepository = new();

        public CountryOfOccurrenceMatchingCheckerTests()
        {
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?> { { "DataExtraction:FuzzySharpCountryMatchThreshold", "80" }, { "DataExtraction:FuzzySharpCountryInitialismThreshold", "80" } })
                .Build();
            _countriesRepository.Setup(x => x.GetNames()).ReturnsAsync(CountryNames());
            _countryOfOccurrenceMatchingChecker = new CountryOfOccurrenceChecker(_mockLogger.Object, _countriesRepository.Object, configuration);
        }
        [Fact]
        public void CountryOfOccurrenceMatcher_MatchFound_ReturnsTrue()
        {
            //Arrange
            var extractedReference = new ExtractedReference
            {
                Title = null!,
                Abstract = null!,
                Authors = [],
                Affiliations = [],
                Doi = null!,
                IssueNumber = null!,
                Volume = null!,
                Issn = null!,
                Year = null!,
                Pages = null!,
                CountryOfOccurrence = new CountryOfOccurrence() { Confidence = 1, Value = "United Kingdom" },
                Keywords = null!,
                JournalTitle = null!
            };

            // Act
            var result = _countryOfOccurrenceMatchingChecker.GetMatchResult(extractedReference);

            // Assert
            Assert.Equal(MatchingStatus.Passed, result.Status);
        }

        [Fact]
        public void CountryOfOccurrenceMatcher_Matched_Less_Than_100Percent_ReturnsUnsure()
        {
            //Arrange
            var extractedReference = new ExtractedReference
            {
                Title = null!,
                Abstract = null!,
                Authors = [],
                Affiliations = [],
                Doi = null!,
                IssueNumber = null!,
                Volume = null!,
                Issn = null!,
                Year = null!,
                Pages = null!,
                CountryOfOccurrence = new CountryOfOccurrence() { Confidence = 1, Value = "Frence" },
                Keywords = null!,
                JournalTitle = null!
            };

            // Act
            var result = _countryOfOccurrenceMatchingChecker.GetMatchResult(extractedReference);

            // Assert
            Assert.Equal(MatchingStatus.Unsure, result.Status);
            Assert.Equal("France", result.MatchedValue);
        }

        [Fact]
        public void CountryOfOccurrenceMatcher_MatchFound_100percent_ReturnsPassed()
        {
            //Arrange
            var extractedReference = new ExtractedReference
            {
                Title = null!,
                Abstract = null!,
                Authors = [],
                Affiliations = [],
                Doi = null!,
                IssueNumber = null!,
                Volume = null!,
                Issn = null!,
                Year = null!,
                Pages = null!,
                CountryOfOccurrence = new CountryOfOccurrence() { Confidence = 1, Value = "UK" },
                Keywords = null!,
                JournalTitle = null!
            };

            // Act
            var result = _countryOfOccurrenceMatchingChecker.GetMatchResult(extractedReference);

            // Assert
            Assert.Equal(MatchingStatus.Passed, result.Status);
            Assert.Equal("United Kingdom", result.MatchedValue);
        }

        [Fact]

        public void CountryOfOccurrenceMatcher_NoMatchFound_Returns_Failed()
        {
            //Arrange
            var extractedReference = new ExtractedReference
            {
                Title = null!,
                Abstract = null!,
                Authors = [],
                Affiliations = [],
                Doi = null!,
                IssueNumber = null!,
                Volume = null!,
                Issn = null!,
                Year = null!,
                Pages = null!,
                CountryOfOccurrence = new CountryOfOccurrence() { Confidence = 1, Value = "testCountry" },
                Keywords = null!,
                JournalTitle = null!
            };

            // Act
            var result = _countryOfOccurrenceMatchingChecker.GetMatchResult(extractedReference);

            // Assert
            Assert.Equal(MatchingStatus.Failed, result.Status);
            Assert.Null(result.MatchedValue);
        }

        private static List<string> CountryNames()
        {
            return new List<string>
            {
                "United Kingdom",
                "United States",
                "Portugal",
                "Germany",
                "India",
                "China",
                "France"
            };
        }

    }
}
