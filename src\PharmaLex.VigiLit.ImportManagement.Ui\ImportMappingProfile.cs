using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui;

public class ImportMappingProfile : Profile
{
    public ImportMappingProfile()
    {
        CreateMap<Import, ImportModel>()
            .ForMember(d => d.ImportType, s => s.MapFrom(x => x.ImportType.GetDescription()))
            .ForMember(d => d.ImportDate, s => s.MapFrom(x => $"{x.ImportDate:dd MMM yyyy}"))
            .ForMember(d => d.StartDate, s => s.MapFrom(x => $"{x.StartDate:dd MMM yyyy HH:mm:ss}"))
            .ForMember(d => d.EndDate, s => s.MapFrom(x => $"{x.EndDate:dd MMM yyyy HH:mm:ss}"))
            .ForMember(d => d.Duration, s => s.MapFrom(x => x.EndDate.HasValue ? string.Format("{0:hh\\:mm\\:ss}", x.EndDate.Value - x.StartDate) : string.Empty))
            .ForMember(d => d.ImportStatusType, s => s.MapFrom(x=>x.ImportType == ImportType.File || x.ImportType == ImportType.Manual|| x.ImportType == ImportType.ManualCorrection?x.Message:x.ImportStatusType.GetDescription()))
            .ForMember(d => d.ContractsCount, s => s.MapFrom(x => x.ImportContracts.Select(x => x.ContractId).Distinct().Count()))
            .ForMember(d => d.ReferencesCount, s => s.MapFrom(x => x.ImportContracts.Sum(x => x.ReferencesCount)))
            .ForMember(d => d.NewReferencesCount, s => s.MapFrom(x => x.ImportContracts.Sum(x => x.NewReferencesCount)))
            .ForMember(d => d.UpdatesCount, s => s.MapFrom(x => x.ImportContracts.Sum(x => x.UpdatesCount)))
            .ForMember(d => d.SilentUpdatesCount, s => s.MapFrom(x => x.ImportContracts.Sum(x => x.SilentUpdatesCount)));

        CreateMap<Import, ImportDashboardModel>()
            .ForMember(d => d.ImportType, s => s.MapFrom(x => x.ImportType.GetDescription()))
            .ForMember(d => d.ImportDate, s => s.MapFrom(x => $"{x.ImportDate:dd MMM yyyy}"))
            .ForMember(d => d.Name, s => s.MapFrom(x => string.Format("{0}: {1}", x.ImportType.GetDescription(), $"{x.ImportDate:d MMM yyyy}")));

        CreateMap<ImportReference, ImportReferenceModel>().ReverseMap();

        CreateMap<FailedImportFile, ImportReference>();
    }
}
