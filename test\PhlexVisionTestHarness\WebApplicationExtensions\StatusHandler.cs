﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Console = PhlexVisionTestHarness.CrossCutting.ConsoleEx;

namespace PhlexVisionTestHarness.WebApplicationExtensions;

internal static class StatusHandler
{
    private const string CorrelationIdHeader = "X-Correlation-ID";

    public static void AddStatusHandler(this WebApplication app)
    {
        app.MapPost("/status/VigiLit", async (HttpContext context) =>
        {
            using var reader = new StreamReader(context.Request.Body);
            var statusMessage = await reader.ReadToEndAsync();

            var correlationId = context.Request.Headers[CorrelationIdHeader];

            Console.WriteLine(ConsoleColor.Red, $"Received Status callback {correlationId}:\n{statusMessage}");
            return Results.Ok("Callback Status received");
        });
    }
}