﻿using AutoMapper;
using Moq;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using Xunit;

namespace PharmaLex.VigiLit.ReferenceManagement.Service.Unit.Tests;

public class SpecialSituationServiceTests
{
    private readonly ISpecialSituationService _specialSituationService;
    private readonly Mock<ISpecialSituationRepository> _mockSpecialSituationRepository = new();

    public SpecialSituationServiceTests()
    {
        var config = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<SpecialSituationMappingProfile>();
        });
        var mapper = config.CreateMapper();

        _specialSituationService = new SpecialSituationService(_mockSpecialSituationRepository.Object, mapper);
    }

    [Fact]
    public async Task GetAllAsync_returns_list_of_SpecialSituations() // Only seems to test the mapper?
    {
        // Arrange
        _mockSpecialSituationRepository.Setup(x => x.GetAllAsync())
            .ReturnsAsync(GetSpecialSituations());

        // Act
        var result = await _specialSituationService.GetAllAsync();

        // Assert
        Assert.Equal(5, result.Count());
    }

    private List<SpecialSituation> GetSpecialSituations()
    {
        return
        [
            new FakeSpecialSituation(1) { Name = "Field 1" },
            new FakeSpecialSituation(2) { Name = "Field 2" },
            new FakeSpecialSituation(3) { Name = "Field 3" },
            new FakeSpecialSituation(4) { Name = "Field 4" },
            new FakeSpecialSituation(5) { Name = "Field 5" }
        ];
    }
}