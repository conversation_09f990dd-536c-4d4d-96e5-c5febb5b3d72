﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36310.24
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PhlexVisionTestHarness", "test\PhlexVisionTestHarness\PhlexVisionTestHarness.csproj", "{23149F40-82CB-A311-DDB8-3C4CD6EBEF42}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.DataExtraction.Service", "src\PharmaLex.VigiLit.DataExtraction.Service\PharmaLex.VigiLit.DataExtraction.Service.csproj", "{76112B2E-C6B0-2CC9-4599-3147D9151F01}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{23149F40-82CB-A311-DDB8-3C4CD6EBEF42}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{23149F40-82CB-A311-DDB8-3C4CD6EBEF42}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{23149F40-82CB-A311-DDB8-3C4CD6EBEF42}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{23149F40-82CB-A311-DDB8-3C4CD6EBEF42}.Release|Any CPU.Build.0 = Release|Any CPU
		{76112B2E-C6B0-2CC9-4599-3147D9151F01}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{76112B2E-C6B0-2CC9-4599-3147D9151F01}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{76112B2E-C6B0-2CC9-4599-3147D9151F01}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{76112B2E-C6B0-2CC9-4599-3147D9151F01}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {005660BA-EC4F-49C6-B171-53C825E132A6}
	EndGlobalSection
EndGlobal
