﻿namespace PhlexVisionTestHarness.CrossCutting;

internal static class ConsoleEx
{
    private static readonly object Lock = new();

    public static void WriteLine(ConsoleColor colour, object message)
    {
        lock (Lock)
        {
            var colourNow = Console.ForegroundColor;
            Console.ForegroundColor = colour;
            Console.WriteLine(message);
            Console.ForegroundColor = colourNow;
        }
    }

    public static string? ReadLine()
    {
        return Console.ReadLine();
    }
}