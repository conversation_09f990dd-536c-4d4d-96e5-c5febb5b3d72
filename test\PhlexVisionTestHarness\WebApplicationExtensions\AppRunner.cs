﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;

namespace PhlexVisionTestHarness.WebApplicationExtensions;

internal static class AppRunner
{
    public static void Run(this WebApplication app, ServiceProvider provider)
    {
        var responseHandler = provider.GetService<ICallbackHandler>();
        var dataExtractor = provider.GetService<IMetaDataExtractor>();

        app.AddErrorHandler();
        app.AddSuccessHandler(responseHandler!, dataExtractor!);
        app.AddStatusHandler();
        app.AddDocumentDownloadHandler();

#pragma warning disable S1075
        var url = "https://localhost:8080";
        app.Run(url);
#pragma warning restore S1075
    }
}