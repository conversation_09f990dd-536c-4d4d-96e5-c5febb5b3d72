﻿using Apify.SDK.Model;
using Task = System.Threading.Tasks.Task;

namespace PharmaLex.VigiLit.Scraping.Service.Interfaces;

public interface IApifyScheduleService
{
    Task CreateScheduleForTaskAsync(string taskId, string scheduleName, string cronExpression, CancellationToken cancellationToken = default);

    Task UpdateScheduleAsync(string scheduleId, string taskId, CancellationToken cancellationToken = default);

    Task<GetListOfSchedulesResponse> GetSchedulesAsync(CancellationToken cancellationToken = default);
}
