﻿using AutoFixture;
using Phlex.Core.Apify.Webhook.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests;

public static class Fake
{
    private static readonly Random Random = new();
    public static readonly Fixture fixture = new();

    public static class Journal
    {
        public static int Id => fixture.Create<int>();
        public static string Name => fixture.Create<string>();
        public static string Url => $"https://{fixture.Create<string>().ToLower()}.com";
        public static string Issn => fixture.Create<string>();
        public static string CronExpression => "0 9 * * 1";
        public static bool Enabled => fixture.Create<bool>();
        public static int CountryId => fixture.Create<int>();
        public static string CountryName => fixture.Create<string>();
        public static string CreatedBy => fixture.Create<string>();
        public static string LastUpdatedBy => fixture.Create<string>();
    }

    public static class Apify
    {
        public static string ActorTaskId => fixture.Create<string>();
        public static string DatasetId => fixture.Create<string>();
        public static string KeyValueStoreId => fixture.Create<string>();
        public static string RunId => fixture.Create<string>();
        public static string TaskId => fixture.Create<string>();
        public static string ScheduleId => fixture.Create<string>();
        public static string TaskName => $"Task_{fixture.Create<string>()}";
        public static string ScheduleName => $"Schedule_{fixture.Create<string>()}";
        public static string CronExpression => "0 9 * * 1";
        public static string FileName => $"{fixture.Create<string>().ToLower()}.pdf";
        public static string BlobPath => $"scraping/{fixture.Create<string>()}/{FileName}";

        public static ApifyWebhookPayload WebhookPayload(string? actorTaskId = null, string? datasetId = null, string? keyValueStoreId = null, string? runId = null)
        {
            return new ApifyWebhookPayload
            {
                createdAt = DateTime.UtcNow,
                resource = new Resource
                {
                    actorTaskId = actorTaskId ?? ActorTaskId,
                    defaultDatasetId = datasetId ?? DatasetId,
                    defaultKeyValueStoreId = keyValueStoreId ?? KeyValueStoreId
                },
                eventData = new EventData
                {
                    actorRunId = runId ?? RunId
                }
            };
        }

        public static List<string> BlobPaths(int count = 2)
        {
            return Enumerable.Range(0, count)
                .Select(_ => BlobPath)
                .ToList();
        }

        public static List<string> Urls(int count = 1)
        {
            return Enumerable.Range(0, count)
                .Select(_ => Journal.Url)
                .ToList();
        }

        public static Dictionary<string, string> Metadata(string? actorTaskId = null)
        {
            var metadata = new Dictionary<string, string>
            {
                ["ProcessedAt"] = DateTime.UtcNow.ToString("O")
            };

            if (!string.IsNullOrEmpty(actorTaskId))
            {
                metadata["ActorTaskId"] = actorTaskId;
            }

            return metadata;
        }
    }

    public static string GetRandomString(int length)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[Random.Next(s.Length)]).ToArray());
    }

    public static int GetRandomInt(int min = 1, int max = 1000)
    {
        return Random.Next(min, max);
    }

    public static bool GetRandomBool()
    {
        return fixture.Create<bool>();
    }

    public static DateTime GetRandomDateTime()
    {
        return fixture.Create<DateTime>();
    }
}
