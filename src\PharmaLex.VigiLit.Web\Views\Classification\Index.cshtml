@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;
@model PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule.ClassifyReferenceModel

@{
    ViewData["Title"] = "Classification";
}

<div id="app" v-cloak>
    @Html.AntiForgeryToken()

    <div class="sub-header">
        <h2 v-if="selectedImport">Classification: {{selectedImport.importName}}</h2>
        <h2 v-else>Classification: No Import Selected</h2>
        <div class="controls">
            <template v-if="selectedImport">
                <button v-if="isSigned" disabled class="btn-default">Signed</button>
                <button v-else :disabled="!signEnabled" v-on:click.prevent.stop="showSignatureDialog=true" class="btn-default">Sign</button>
            </template>
        </div>
    </div>

    <section class="card-container">

        <ul class="tabs borderless" v-if="displayTabs">
            <li @@click="getAllPreClassified" v-bind:class="{ active: activeTab == 'preclassified' }">
                <a @@click.prevent href="">Preclassified</a>
                <span class="tab-badge">{{preClassifiedReferenceCount}}</span>
            </li>
            <li @@click="getAllClassified" v-bind:class="{ active: activeTab == 'completed' }">
                <a @@click.prevent href="">Completed</a>
                <span class="tab-badge completed-background">{{completedCount}}</span>
            </li>
            <li class="separator"></li>
            <li class="nolink">
                <span>To-Do</span>
                <span class="tab-badge statistics-background">{{todoCount}}</span>
            </li>
        </ul>

        <div v-if="!selectedImport" class="no-import-selected">
            <i class="m-icon neutral-color">info</i>
            <h2>No Import Selected</h2>
        </div>

        <div v-if="isCompleted" class="classification-completed">
            <i class="m-icon success-color">done</i>
            <h2>Ready to sign.</h2>
        </div>

        <div v-if="isSigned" class="classification-signed">
            <i class="m-icon success-color">done</i>
            <h2>Signed.</h2>
        </div>

        <div v-if="referenceClassifications && activeTab == 'preclassified'">
            <div v-for="referenceClassification in pagedReferenceClassifications" :key="referenceClassification.id" class="reference-container-row mb-2">
                <reference-info :reference-classification="referenceClassification"
                                v-on:translation="downloadTranslation"
                                v-on:original="downloadOriginalFile"></reference-info>
                <div class="reference-content">
                    <reference :reference="referenceClassification.reference"
                               :substance="referenceClassification.substance"></reference>
                </div>
                <div class="reference-classification">
                    <classification-form v-model:reference-classification="referenceClassification"
                                         :classification-categories="classificationCategories"
                                         :countries="countries"
                                         :classification-mode="'classification'"
                                         :show-special-situations="true"
                                         :special-situations="referenceClassification.specialSituationList"
                                         v-on:approve="approve"
                                         header="Classification">
                    </classification-form>
                </div>
            </div>
        </div>

        <div v-if="classifiedReferenceClassifications && activeTab == 'completed'">
            <div v-for="referenceClassification in pagedReferenceClassifications" class="reference-container-row mb-2">
                <reference-info :reference-classification="referenceClassification"
                                v-on:translation="downloadTranslation"
                                v-on:original="downloadOriginalFile"></reference-info>

                <div class="reference-content">
                    <reference :reference="referenceClassification.reference"
                               :substance="referenceClassification.substance"></reference>
                </div>
                <div class="reference-classification">
                    <classification-form v-model:reference-classification="referenceClassification"
                                         :classification-categories="classificationCategories"
                                         :countries="countries"
                                         :classification-mode="'reclassification'"
                                         :is-country-of-occurrence-verified="false"
                                         :show-special-situations="true"
                                         :special-situations="referenceClassification.specialSituationList"
                                         v-on:reapprove="reapprove"
                                         header="Classification">
                    </classification-form>
                </div>
            </div>
        </div>
    </section>

    <redirect-dialog :show="showSignatureDialog"
                     :title="'Sign all classified references'"
                     :button-text="'Sign'"
                     :hint="'Once you have successfully confirmed your identity in this way you will be redirected back to VigiLit.'"
                     :message="'You are about to sign all classified references. When you are ready, select the \'Sign\' button and you will be redirected to a new page where you will be asked to re-enter your password to confirm your identity.'"
                     :redirect-url="'/Classification/Sign'"
                     v-on:close="showSignatureDialog=false">
    </redirect-dialog>

    <data-table-pager v-if="items?.length"
                      :item-count="items.length"
                      :page-index="pageIndex"
                      :page-size="size"
                      :location="'bottom'"
                      :resources="strings.pager"
                      :total-items="items.length"
                      v-on:page-index-change="changePage"
                      v-on:page-size-change="changePageSize">
    </data-table-pager>
</div>

@section Scripts {

    <script type="text/javascript">
        let model = @Html.Raw(AntiXss.ToJson(Model));

        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        var pageConfig = {
            appElement: "#app",
            data: function () {
                return {
                    selectedImport: model.selectedImport,
                    classificationCategories: model.classificationCategories,
                    countries: model.countries,
                    todoCount: model.todoCount,
                    completedCount: model.completedCount,
                    referenceClassifications: [],
                    classifiedReferenceClassifications: [],
                    activeTab: '',
                    showSignatureDialog: false,
                    initialLoadComplete: false,
                    specialSituationList: {
                        listItems: [],
                        selectedIds: []
                    },
                    strings: {
                        pager: {
                            showingFormat: 'Showing {} to {} of {} entries',
                            showingFilteredFormat: '(filtered from {})',
                            pageSize: 'Page size',
                            first: 'First',
                            previous: 'Previous',
                            next: 'Next',
                            last: 'Last',
                        }
                    },

                    size: 25,
                    pageIndex: 0,
                    pageSize: 25,
                    pagerTotalItems: null,
                    items: null,
                    storageKey: `${window.location.href}(${this.$attrs.id || ''})`,
                };
            },
            methods: {
                approve: function (referenceClassification) {

                    referenceClassification.specialSituations = this.formatBracketNumbers(referenceClassification.specialSituationList.selectedIds);

                    fetch(`/Classification/Classify`, {
                        method: "POST",
                        body: JSON.stringify(referenceClassification),
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(() => {
                            this.completedCount++;
                            this.referenceClassifications = this.referenceClassifications.filter(r => r.id !== referenceClassification.id);
                            this.items = this.referenceClassifications;
                            this.loadItems();

                            plx.toast.show('The reference was approved.', 2, 'confirm', null, 2500);
                        })
                        .catch(error => {
                            plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                        });
                },
                reapprove: function (referenceClassification) {

                    referenceClassification.specialSituations = this.formatBracketNumbers(referenceClassification.specialSituationList.selectedIds);

                    fetch(`/Classification/ReClassify`, {
                        method: "POST",
                        body: JSON.stringify(referenceClassification),
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(() => {
                            const index = this.classifiedReferenceClassifications.findIndex(c => c.id === referenceClassification.id);
                            if (index > -1) {
                                this.classifiedReferenceClassifications[index].buttonDisabled = true;
                            }

                            plx.toast.show('The reference was updated.', 2, 'confirm', null, 2500);
                        })
                        .catch(error => {
                            plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                        });
                },
                getAllPreClassified: function () {
                    fetch(`/Classification/GetAllPreClassified`, {
                        method: "GET"
                    })
                        .then(res => {
                            if (!res.ok)
                                throw res;

                            return res.json();
                        })
                        .then((res) => {
                            this.activeTab = 'preclassified';
                            this.referenceClassifications = res;

                            this.referenceClassifications.forEach(x => {

                                x.specialSituationList = {
                                    listItems: [...this.specialSituationList.listItems],
                                    selectedIds: []
                                }

                                if (this.isPotentialCase(x) && x.specialSituations) {
                                    x.specialSituationList.selectedIds = this.parseBracketedNumbers(x.specialSituations);
                                }
                            });

                            this.items = this.referenceClassifications;
                            this.matchCountryOfOccurrence(this.items);
                            this.changePage(0);

                            this.initialLoadComplete = true;
                        })
                        .catch(error => {
                            plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                        });
                },
                isPotentialCase(referenceClassification) {
                    return referenceClassification.classificationCategoryId == 1;
                },
                parseBracketedNumbers: function (str) {
                    return str
                        .slice(1, -1)
                        .split("][")
                        .map(Number);
                },
                formatBracketNumbers: function (arr) {
                    return arr.sort((a, b) => a - b).map(x => `[${x}]`).join('');
                },
                getAllClassified: function () {
                    fetch(`/Classification/GetAllClassified`, {
                        method: "GET"
                    })
                        .then(res => {
                            if (!res.ok)
                                throw res;

                            return res.json();
                        })
                        .then((res) => {
                            for (let classification of res) {
                                classification.buttonDisabled = true;
                            }
                            this.activeTab = 'completed';
                            this.classifiedReferenceClassifications = res;

                            this.items = this.classifiedReferenceClassifications;

                            this.items.forEach(x => {

                                x.specialSituationList = {
                                    listItems: [...this.specialSituationList.listItems],
                                    selectedIds: []
                                }

                                if (this.isPotentialCase(x) && x.specialSituations) {
                                    x.specialSituationList.selectedIds = this.parseBracketedNumbers(x.specialSituations);
                                }
                            });

                            this.changePage(0);
                        })
                        .catch(error => {
                            plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                        });
                },
                changePage(index) {
                    this.pageIndex = +index;
                    this.updateState({ pageIndex: this.pageIndex });
                    this.loadItems();
                },
                changePageSize(size) {
                    this.changePage(0);
                    this.size = +size;
                    this.updateState({ pageSize: this.size });
                    this.loadItems();
                },
                loadState() {
                    try {
                        let stateString = localStorage.getItem(this.storageKey);
                        let state = stateString ? JSON.parse(stateString) : { date: new Date() };
                        let now = new Date();
                        let stateDate = new Date(state.date);

                        if (now.getDay() === stateDate.getDay() &&
                            now.getMonth() === stateDate.getMonth() &&
                            now.getYear() === stateDate.getYear()) {

                            this.size = state.pageSize || this.size;
                            if (this.data.paged) {
                                this.pageIndex = state.hasOwnProperty('pageIndex') ? state.pageIndex : this.pageIndex;
                            } else {
                                this.$nextTick(function () {
                                    this.pageIndex = state.hasOwnProperty('pageIndex') ? state.pageIndex : this.pageIndex;
                                });
                            }
                        } else localStorage.removeItem(this.storageKey);
                    } catch {
                        localStorage.removeItem(this.storageKey);
                    }
                },
                updateState(partial) {
                    let stateString = localStorage.getItem(this.storageKey);
                    let state = stateString ? JSON.parse(stateString) : {};

                    partial = { ...partial, date: new Date() };
                    localStorage.setItem(this.storageKey, JSON.stringify(Object.assign(state, partial)));
                },
                loadItems: function () {
                    this.pageSize = this.size;
                    let startIndex = this.pageIndex * this.pageSize;
                    let endIndex = (this.pageIndex + 1) * this.pageSize <= this.items.length
                        ? (this.pageIndex + 1) * this.pageSize : this.items.length;
                    this.pagedReferenceClassifications = this.items.slice(startIndex, endIndex);
                },
                matchCountryOfOccurrence: function (items) {
                    for (let i = 0; i < items.length; i++) {
                        items[i].countryOfOccurrence = this.countries.find(c => c === items[i].reference?.countryOfOccurrence)
                            ? items[i]?.reference?.countryOfOccurrence : '';
                    }
                    Object.assign(items, this.items);
                },
                downloadFile(metaData, fileNameKey) {
                    let jsonMetaData = JSON.parse(metaData);
                    let batchId = jsonMetaData.BatchId;
                    let filename = jsonMetaData[fileNameKey];

                    if (!batchId || !filename) {
                        plx.toast.show('Batch ID or File Name is missing.', 2, 'failed', null, 2000);
                        return;
                    }

                    const url = `/PreClassification/Download/${batchId}/${filename}`;

                    const onFailure = function (xhr) {
                        let errorMessage = 'Downloading file failed.';
                        if (xhr && xhr.responseText) {
                            try {
                                const error = JSON.parse(xhr.responseText);
                                if (error && error.message) {
                                    errorMessage = error.message;
                                }
                            } catch (e) {
                                console.log(e);
                            }
                        }
                        plx.toast.show(errorMessage, 2, 'failed', null, 2000);
                    };

                    const onComplete = function () {
                        plx.toast.show(`File "${filename}" downloaded successfully.`, 5, 'confirm', null, 2500);
                    };

                    DownloadFile.fromUrl(url, null, null, onFailure, onComplete);
                },
                downloadTranslation: function (metaData) {
                    this.downloadFile(metaData, 'TranslatedFileName');
                },

                downloadOriginalFile: function (metaData) {
                    this.downloadFile(metaData, 'FileName');
                }
            },
            computed: {
                signEnabled() {
                    // none for the pre-assessor to do
                    // none for the master assessor to do
                    return this.todoCount === 0 && this.preClassifiedReferenceCount === 0;
                },
                preClassifiedReferenceCount() {
                    return this.referenceClassifications.length;
                },
                displayTabs() {
                    return this.selectedImport && this.initialLoadComplete;
                },
                isCompleted() {
                    return this.signEnabled && !this.isSigned && this.initialLoadComplete;
                },
                isSigned() {
                    return this.selectedImport && this.selectedImport.importDashboardStatusType === 'Signed';
                }
            },
            created() {
                if (model.specialSituationList) {
                    this.specialSituationList.listItems = [...model.specialSituationList];
                }

                this.getAllPreClassified();
            }
        };

    </script>
}

@section VueComponentScripts {
    <partial name="Components/ClassificationForm" />
    <partial name="Components/ReferenceComponent" />
    <partial name="Components/RedirectDialog" />
    <partial name="Components/AuditTrailSelectionComponent" />
    <partial name="Components/ReferenceInfo" />
    <partial name="Components/MultiSelect" />
    <partial name="Components/Vue3/FilteredTable" />
}