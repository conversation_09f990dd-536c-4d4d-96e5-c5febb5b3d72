﻿using Microsoft.Extensions.Configuration;
using System.Text.Json;
using static PharmaLex.VigiLit.DataExtraction.Service.PhlexVision.PhlexVisionConstants;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision.Factories;

internal class SinglePromptDocumentProcessModelFactory : IDocumentProcessModelFactory
{
    private readonly IConfiguration _configuration;

    public SinglePromptDocumentProcessModelFactory(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public string GetDocumentProcessModel(string token, ExtractRequest extractRequest)
    {
        var baseUrl = _configuration[PhlexVisionCallbackBaseUrl];

        var documentLocationUrl = $"{baseUrl}DownloadDocument/{extractRequest.BatchId}/{extractRequest.FileName}";

        var result = JsonSerializer.Serialize(
            new
            {
                documentId = extractRequest.BatchId,
                DocumentDownloadUrl = documentLocationUrl,
                successCallbackUrl = $"{baseUrl}success/{extractRequest.BatchId}",
                errorCallbackUrl = $"{baseUrl}error/{extractRequest.BatchId}",
                BearerToken = token,
                ConfigId = _configuration[OpenAiConfigId],
                Stages = (string[])["Translate", "MetadataExtractor"],
                Priority = "Urgent",
                FileExtension = "pdf"
            });

        return result;
    }
}