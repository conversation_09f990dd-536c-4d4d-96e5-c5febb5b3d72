﻿using FuzzySharp;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.ImportManagement.Client;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl
{
    internal class CountryOfOccurrenceChecker : IExtractionMatchValidator
    {
        private List<string> _countriesList = new();
        private readonly ICountryRepository _countryRepository;
        private readonly int _fuzzySharpCountryMatchThreshold;
        private readonly int _fuzzySharpCountryInitialismThreshold;
        private readonly ILogger<CountryOfOccurrenceChecker> _logger;
        private Task? _loadingTask;

        public CountryOfOccurrenceChecker(ILogger<CountryOfOccurrenceChecker> logger, ICountryRepository countryRepository, IConfiguration configuration)
        {
            _fuzzySharpCountryMatchThreshold = configuration.GetValue<int>("DataExtraction:FuzzySharpCountryMatchThreshold");
            _fuzzySharpCountryInitialismThreshold = configuration.GetValue<int>("DataExtraction:FuzzySharpCountryInitialismThreshold");
            _countryRepository = countryRepository;
            _logger = logger;

        }
        public MatchValidationResult GetMatchResult(ExtractedReference extractedReference)
        {
            CountriesInitialized();
            var countryOfOccurrence = extractedReference.CountryOfOccurrence.Value;
            _logger.LogInformation("Extracted Country of Occurrence: {CountryOfOccurrence}", countryOfOccurrence);

            if (_countriesList.Count == 0)
            {
                _logger.LogWarning("Countries count is zero");
                return new MatchValidationResult
                {
                    Status = MatchingStatus.Failed,
                    MatchedValue = null
                };
            }

            var countryOfOccurrenceUpper = countryOfOccurrence.ToUpper();

            // Get all country matches with their scores
            var countryMatches = _countriesList
                .Select(originalCountry =>
                {
                    var upperCountry = originalCountry.ToUpper();
                    var standardRatio = Fuzz.Ratio(countryOfOccurrenceUpper, upperCountry);
                    var initialismRatio = Fuzz.TokenInitialismRatio(countryOfOccurrenceUpper, upperCountry);

                    // Check if either matching type meets its threshold
                    var standardMatch = standardRatio >= _fuzzySharpCountryMatchThreshold;
                    var initialismMatch = initialismRatio >= _fuzzySharpCountryInitialismThreshold;
                    var anyMatch = standardMatch || initialismMatch;

                    // Use the higher of the two ratios as the final score
                    var bestScore = Math.Max(standardRatio, initialismRatio);

                    return new MatchCheckerModel
                    {
                        OriginalValue = originalCountry,
                        Score = bestScore,
                        StandardRatio = standardRatio,
                        InitialismRatio = initialismRatio,
                        HasValidMatch = anyMatch
                    };
                })
                .Where(match => match.HasValidMatch) // Only consider countries that meet at least one threshold
                .OrderByDescending(match => match.Score)
                .ToList();

            var bestMatch = countryMatches.FirstOrDefault();

            if (bestMatch == null)
            {
                _logger.LogWarning("Country of Occurrence: {CountryOfOccurrence} could not be matched to any country above threshold.",
                    countryOfOccurrence);

                return new MatchValidationResult
                {
                    Status = MatchingStatus.Failed,
                    MatchedValue = null
                };
            }

            _logger.LogDebug("Best country match: '{Input}' -> '{Target}' (Score: {Score}, Standard: {StandardRatio}, Initialism: {InitialismRatio})",
                countryOfOccurrenceUpper, bestMatch.OriginalValue?.ToUpper(), bestMatch.Score, bestMatch.StandardRatio, bestMatch.InitialismRatio);

            // Determine status based on score
            if (bestMatch.Score == 100)
            {
                _logger.LogInformation("Country of Occurrence: {CountryOfOccurrence} perfectly matched to: {MatchedCountry}.",
                    countryOfOccurrence, bestMatch.OriginalValue);

                return new MatchValidationResult
                {
                    Status = MatchingStatus.Passed,
                    MatchedValue = bestMatch.OriginalValue
                };
            }
            else
            {
                // Since we only include matches that meet thresholds, anything remaining is "Unsure"
                _logger.LogInformation("Country of Occurrence: {CountryOfOccurrence} matched with uncertainty to: {MatchedCountry} (Score: {Score}).",
                    countryOfOccurrence, bestMatch.OriginalValue, bestMatch.Score);

                return new MatchValidationResult
                {
                    Status = MatchingStatus.Unsure,
                    MatchedValue = bestMatch.OriginalValue
                };
            }

        }



        private void CountriesInitialized()
        {
            if (_countriesList.Count > 0)
                return;
            _loadingTask ??= PopulateCountriesName();
            _loadingTask.GetAwaiter().GetResult();

        }

        private async Task PopulateCountriesName()
        {
            _countriesList = (await _countryRepository.GetNames()).ToList();
        }
    }
}
