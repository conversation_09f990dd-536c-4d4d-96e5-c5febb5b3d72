# Introduction

MetaDataExtraction test harness is a console application for quickly PhlexVision from a developers machine without having to mess about with the main VigiLit code.

# Running the application in local environment

To allow https to be hosted locally execute the following code at the command prompt:

```
dotnet dev-certs https --trust
```

Download the CloudFlare tunnel application from this link: https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe

Run the downloaded executable at the command line as follows:

```
cloudflared-windows-amd64.exe tunnel --url https://localhost:8080
```

Make a note of the quick tunnel URL and set the CalbackBaseUrl in user secrets below. Add a trailing slash.

Add the following to User Secrets:

  "PhlexVision:Secret": "--- get from vision team ---",
  "PhlexVision:OpenAiConfigId": "6981a1f6-828d-4928-a1ce-09a65aa0fc39",
  "PhlexVision:Url": "https://phlexvision-integration.phlexglobal.com/api/ocrapi/v1/documents",
  "PhlexVision:ConsumerKeyHeaderValue": "mock",
  "PhlexVision:CallbackBaseUrl": "--- get from CloudFlareD ---"

  Use the "Debug Data Extraction" run profile (this will run the ImportApp, and Docker Compose and the test harness).

  Visiting the URL http://localhost:7071/api/ImportDataExtractProcessor_ProcessEnqueuedImports in a browser will start a data extraction call to Vision and return the result in the Test harness.
