﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Time.Testing;
using Moq;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Service.Auditing;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.MessageBroker.Contracts;

namespace PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests;

public class AuditHandlerTests
{
    private readonly IAuditHandler _auditHandler;
    private readonly Mock<ILogger<AuditHandler>> _mockLogger = new();
    private readonly Mock<IImportingImportRepository> _importRepository = new();
    private readonly FakeTimeProvider _fakeTimeProvider = new FakeTimeProvider();

    public AuditHandlerTests()
    {
        _auditHandler = new AuditHandler(_mockLogger.Object, _importRepository.Object, _fakeTimeProvider);
    }

    [Fact]
    public async Task AuditHandler_CorrelationIdMatchFound_UpdatesImportRepository()
    {
        //Arrange
        var testGuid = Guid.NewGuid();
        _fakeTimeProvider.SetUtcNow(new DateTimeOffset(2025,8,11,10,0,0 , TimeSpan.Zero));
        var import = new Import
        {
            CorrelationId = testGuid,
            ImportStatusType = ImportStatusType.Queued,
            ImportTriggerType = ImportTriggerType.Manual
        };
        var testStatusChangedEvent = new StatusChangedEvent("File Import", testGuid, _fakeTimeProvider.GetUtcNow().DateTime, "<EMAIL>", "Started");
        _importRepository
            .Setup(x => x.GetByCorrelationId(testGuid))
            .ReturnsAsync(import);

        //Act 
        await _auditHandler.Consume(testStatusChangedEvent);

        //Assert
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Exactly(1));
    }
}