﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="Views\Search\Index.cshtml" />
		<None Remove="Views\Search\PrintPreview.cshtml" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Views\Search\Index.cshtml" />
		<EmbeddedResource Include="Views\Search\PrintPreview.cshtml" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
		<PackageReference Include="Microsoft.FeatureManagement" Version="4.2.1" />
		<PackageReference Include="PharmaLex.Caching.Data" Version="8.0.0.202" />
		<PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\PharmaLex.Core.UserSessionManagement\PharmaLex.Core.UserSessionManagement.csproj" />
		<ProjectReference Include="..\PharmaLex.Core.Web\PharmaLex.Core.Web.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj" />
	</ItemGroup>


</Project>