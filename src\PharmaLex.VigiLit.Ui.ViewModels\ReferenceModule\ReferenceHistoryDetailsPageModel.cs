﻿using System.Collections.Generic;

namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class ReferenceHistoryDetailsPageModel
{
    public IEnumerable<ReferenceHistoryActionModel> HistoryActions { get; set; }

    public ReferenceDetailedModel ReferenceDetails { get; set; }

    public ReferenceDetailedModel PreviousReferenceDetails { get; set; }

    public ReferenceClassificationWithSubstanceModel ReferenceClassification { get; set; }

    public bool IsEditable { get; set; }

    public IEnumerable<SpecialSituationModel>  SpecialSituationList { get; set; }
}