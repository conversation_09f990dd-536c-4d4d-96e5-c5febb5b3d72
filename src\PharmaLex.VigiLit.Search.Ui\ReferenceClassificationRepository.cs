﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Search.Ui.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Search.Ui;

internal class ReferenceClassificationRepository : TrackingGenericRepository<ReferenceClassification>, IReferenceClassificationRepository
{
    protected readonly IMapper _mapper;

    public ReferenceClassificationRepository(PlxDbContext context, IMapper mapper, IUserContext userContext)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<ReferenceClassificationSupportModel>> Search(ClassificationSearchRequest request, User user, int? maxRows)
    {
        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.Reference)
            .AsNoTracking();

        query = AddSecurityCriteria(query, user, request.CompanyId);
        query = await AddFilterCriteria(query, request);
        query = query.OrderByDescending(rc => rc.LastUpdatedDate);

        // 100 rows for UI. 10,000 rows for export. 
        if (maxRows.HasValue)
        {
            query = query.Take(maxRows.Value);
        }

        return await _mapper.ProjectTo<ReferenceClassificationSupportModel>(query).ToListAsync();
    }

    public async Task<IEnumerable<PrintPreviewSearchResultModel>> PrintPreview(ClassificationSearchRequest request, User user, int? maxRows)
    {
        var query = context.Set<ReferenceClassification>()
            .Include(rc => rc.Reference)
            .AsNoTracking();

        query = AddSecurityCriteria(query, user, request.CompanyId);
        query = await AddFilterCriteria(query, request);
        query = query.OrderByDescending(rc => rc.LastUpdatedDate);

        // Limit number of rows returned
        if (maxRows.HasValue)
        {
            query = query.Take(maxRows.Value);
        }

        return await _mapper.ProjectTo<PrintPreviewSearchResultModel>(query).ToListAsync();
    }

    private IQueryable<ReferenceClassification> AddSecurityCriteria(IQueryable<ReferenceClassification> query, User user, int companyId)
    {
        if (user.IsCompanyUser())
        {
            if (!user.HasActiveCompany())
            {
                query = query.Where(rc => false);
            }
            else
            {
                query = query.Where(rc => context.Set<CompanyInterest>().Any(ci => ci.CompanyId == user.GetActiveCompanyId() && ci.ReferenceClassificationId == rc.Id));

                // External users can only see classified classifications
                query = query.Where(rc => rc.ClassificationCategoryId.HasValue);
            }
        }
        else
        {
            if (companyId > 0)
            {
                query = query.Where(rc => context.Set<CompanyInterest>().Any(ci => ci.CompanyId == companyId && ci.ReferenceClassificationId == rc.Id));
            }
        }

        return query;
    }

    private async Task<IQueryable<ReferenceClassification>> AddFilterCriteria(IQueryable<ReferenceClassification> query, ClassificationSearchRequest request)
    {
        if (!string.IsNullOrWhiteSpace(request.Term))
        {
            var termTrimmed = request.Term?.Trim();
            _ = int.TryParse(termTrimmed, out int termInt);

            // Have to fetch the reference IDs in a separate query for perf.
            var referenceIds = await context.Set<Reference>()
                .AsNoTracking()
                .Where(r => r.SourceId == termTrimmed || r.Doi == termTrimmed)
                .Select(r => r.Id)
                .ToListAsync();

            query = query.Where(rc => referenceIds.Contains(rc.ReferenceId) || rc.Id == termInt);
        }

        query = AddDateQueryTerms(query, request.CreatedFrom, request.CreatedTo, request.LastUpdatedFrom, request.LastUpdatedTo);

        if (request.Substances.SelectedIds != null && request.Substances.SelectedIds.Any())
        {
            query = query.Where(rc => request.Substances.SelectedIds.ToList().Contains(rc.SubstanceId));
        }

        if (request.ClassificationCategories.SelectedIds != null && request.ClassificationCategories.SelectedIds.Any())
        {
            query = query.Where(rc => request.ClassificationCategories.SelectedIds.ToList().Contains((int)rc.ClassificationCategoryId!));
        }

        if (request.SpecialSituations.SelectedIds != null && request.SpecialSituations.SelectedIds.Any())
        {
            var specialSituationIds = request.SpecialSituations.SelectedIds;
            foreach (var id in specialSituationIds)
            {
                query = query.Where(rc => rc.SpecialSituations.Contains($"[{id}]"));
            }
        }

        if (!string.IsNullOrWhiteSpace(request.PSUR) && (Enum.TryParse(request.PSUR?.Trim(), out PSURRelevanceAbstract psurEnumValue)))
        {
            query = query.Where(rc => rc.PSURRelevanceAbstract == psurEnumValue);
        }

        query = AddFullTextQueryTerms(query, request.Title, request.MeshTerm, request.Keyword, request.SearchMeshTermWithOr, request.SearchKeywordWithOr);

        return query;
    }

    private static IQueryable<ReferenceClassification> AddDateQueryTerms(IQueryable<ReferenceClassification> query, DateTime? createdFrom, DateTime? createdTo, DateTime? lastUpdatedFrom, DateTime? lastUpdatedTo)
    {
        if (createdFrom.HasValue)
        {
            query = query.Where(rc => rc.CreatedDate >= createdFrom.Value);
        }

        if (createdTo.HasValue)
        {
            query = query.Where(rc => rc.CreatedDate <= createdTo.Value.AddDays(1));
        }

        if (lastUpdatedFrom.HasValue)
        {
            query = query.Where(rc => rc.LastUpdatedDate >= lastUpdatedFrom.Value);
        }

        if (lastUpdatedTo.HasValue)
        {
            query = query.Where(rc => rc.LastUpdatedDate <= lastUpdatedTo.Value.AddDays(1));
        }

        return query;
    }

    private static IQueryable<ReferenceClassification> AddFullTextQueryTerms(IQueryable<ReferenceClassification> query, string title, string meshTerm, string keyword, bool searchMeshTermWithOr, bool searchKeywordWithOr)
    {
        if (!string.IsNullOrWhiteSpace(title))
        {
            query = query.Where(x => EF.Functions.Contains(x.Reference.Title, BuildContainsSearchValue(title, false)));
        }

        if (!string.IsNullOrWhiteSpace(meshTerm))
        {
            query = query.Where(x => EF.Functions.Contains(x.Reference.MeshHeadings, BuildContainsSearchValue(meshTerm, searchMeshTermWithOr)));
        }

        if (!string.IsNullOrWhiteSpace(keyword))
        {
            query = query.Where(x => EF.Functions.Contains(x.Reference.Keywords, BuildContainsSearchValue(keyword, searchKeywordWithOr)));
        }

        return query;
    }

    private static string BuildContainsSearchValue(string requestTerm, bool searchWithOr)
    {
        char[] separators = new char[] { ' ', ',' };
        var terms = requestTerm.Split(separators, StringSplitOptions.RemoveEmptyEntries);
        string containsSearch;
        if (terms.Length > 1)
        {
            if (searchWithOr)
            {
                containsSearch = string.Join(" OR ", terms);
            }
            else
            {
                containsSearch = string.Join(" AND ", terms);
            }
        }
        else
        {
            containsSearch = requestTerm;
        }

        return containsSearch;
    }
}