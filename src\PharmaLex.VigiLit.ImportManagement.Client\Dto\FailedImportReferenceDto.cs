﻿namespace PharmaLex.VigiLit.ImportManagement.Client.Dto
{
    public class FailedImportReferenceDto : ReferenceDto
    {
        public byte AbstractConfidence { get; set; }
        public byte AffiliationTextFirstAuthorConfidence { get; set; }
        public byte AuthorsConfidence { get; set; }
        public byte CountryOfOccurrenceConfidence { get; set; }
        public byte DateRevisedConfidence { get; set; }
        public byte DoiConfidence { get; set; }
        public byte FullPaginationConfidence { get; set; }
        public byte IssnConfidence { get; set; }
        public byte IssueConfidence { get; set; }
        public byte LanguageConfidence { get; set; }
        public byte SourceIdConfidence { get; set; }
        public byte PublicationTypeConfidence { get; set; }
        public byte PublicationYearConfidence { get; set; }
        public byte TitleConfidence { get; set; }
        public byte VolumeConfidence { get; set; }
        public byte VolumeAbbreviationConfidence { get; set; }
        public byte KeywordsConfidence { get; set; }
        public byte MeshHeadingsConfidence { get; set; }
        public byte JournalTitleConfidence { get; set; }

        public bool AbstractConfidenceCheckPassed { get; set; }
        public bool CountryOfOccurrenceConfidenceCheckPassed { get; set; }
        public bool TitleConfidenceCheckPassed { get; set; }
        public bool JournalTitleConfidenceCheckPassed { get; set; }
        public MatchingStatus JournalTitleMatchingStatus { get; set; }
        public string? JournalTitleMatched { get; set; }
        public MatchingStatus CountryOfOccurrenceMatchingStatus { get; set; }
        public string? CountryMatched { get; set; }


        public string? DocumentLocation { get; set; }
        public string? Filename { get; set; }
        public Guid CorrelationId { get; set; }
        public Guid BatchId { get; set; }

    }
}
