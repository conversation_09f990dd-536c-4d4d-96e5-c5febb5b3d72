﻿namespace PharmaLex.VigiLit.MessageBroker.Contracts;

public class StatusChangedEvent
{
    public StatusChangedEvent(string eventSourceName, Guid correlationId, DateTime timeCreated, string user,
        string message)
    {
        EventSourceName = eventSourceName;
        CorrelationId = correlationId;
        TimeCreated = timeCreated;
        User = user;
        Message = message;
    }

    public string EventSourceName { get; set; }
    public Guid CorrelationId { get; set; }
    public DateTime TimeCreated { get; set; }
    public string User { get; set; }
    public string Message { get; set; }
}