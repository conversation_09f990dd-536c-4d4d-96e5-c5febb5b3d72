﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ReferenceManagement.Service;

internal class SpecialSituationService(ISpecialSituationRepository specialSituationRepository,
                                                 IMapper mapper)
                                                 : ISpecialSituationService
{
    public async Task<IEnumerable<SpecialSituationModel>> GetAllAsync()
    {
        var res = await specialSituationRepository.GetAllAsync();
        return mapper.Map<IEnumerable<SpecialSituationModel>>(res);
    }
}