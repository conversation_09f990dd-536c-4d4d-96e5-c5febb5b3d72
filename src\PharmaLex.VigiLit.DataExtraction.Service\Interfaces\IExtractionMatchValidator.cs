﻿using PharmaLex.VigiLit.DataExtraction.Entities.Enums;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.DataExtraction.Service.Interfaces
{
    public interface IExtractionMatchValidator
    {
        public MatchValidationResult GetMatchResult(ExtractedReference extractedReference);
    }
}
